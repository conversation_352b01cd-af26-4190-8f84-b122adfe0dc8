# STM32F4 AD9854 陶晶池串口屏控制说明

## 📋 概述

成功将陶晶池串口屏控制模块集成到STM32F4 AD9854 DDS信号发生器项目中。陶晶池串口屏是一款功能强大的智能显示屏，支持丰富的控件和交互功能，非常适合用于工业控制和仪器仪表应用。

## 🎯 陶晶池串口屏特点

### 硬件特性
- **高分辨率显示**: 支持多种尺寸和分辨率
- **触摸操作**: 电阻式或电容式触摸屏
- **丰富接口**: 串口、USB、SD卡等
- **低功耗设计**: 适合长时间运行

### 软件特性
- **可视化编程**: 使用陶晶池编辑器设计界面
- **丰富控件**: 按钮、文本框、数值框、进度条等
- **事件驱动**: 支持触摸、定时器等事件
- **数据存储**: 支持变量存储和文件操作

## 🔧 硬件连接

### 串口连接 (USART1)
```
STM32F407        陶晶池串口屏
PA9 (TX)    →    RX
PA10 (RX)   ←    TX
GND         →    GND
5V/3.3V     →    VCC (根据屏幕规格)
```

### 通信参数
- **波特率**: 115200 (陶晶池默认)
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **流控制**: 无

## 🎮 控件定义

### 主要控件
| 控件名称 | 类型 | 功能 | 示例 |
|----------|------|------|------|
| t_title | 文本框 | 显示标题 | "AD9854 DDS" |
| t_freq | 文本框 | 显示频率数值 | "5.000" |
| t_freq_unit | 文本框 | 显示频率单位 | "MHz" |
| n_amp | 数值框 | 显示幅度数值 | 100 (表示1.00V) |
| t_amp_unit | 文本框 | 显示幅度单位 | "V" |
| sw_output | 开关 | 输出开关控制 | 0/1 |
| t_status | 文本框 | 显示状态信息 | "READY" |

### 页面定义
| 页面ID | 页面名称 | 功能 |
|--------|----------|------|
| 0 | 主页面 | 参数显示和控制 |
| 1 | 设置页面 | 系统设置 |
| 2 | 信息页面 | 设备信息 |

## 🚀 通信协议

### 陶晶池串口屏协议格式
所有命令都以 `\xff\xff\xff` 结尾

#### 1. 文本赋值
```
格式: 控件名.txt="文本内容"\xff\xff\xff
示例: t_freq.txt="5.000"\xff\xff\xff
```

#### 2. 数值赋值
```
格式: 控件名.val=数值\xff\xff\xff
示例: n_amp.val=100\xff\xff\xff
```

#### 3. 页面跳转
```
格式: page 页面ID\xff\xff\xff
示例: page 1\xff\xff\xff
```

#### 4. 控件可见性
```
格式: vis 控件名,可见性\xff\xff\xff
示例: vis t_status,1\xff\xff\xff
```

#### 5. 获取属性
```
格式: get 控件名.属性\xff\xff\xff
示例: get sw_output.val\xff\xff\xff
```

## 📊 API函数说明

### 基础通信函数

#### TJC_SendText()
```c
void TJC_SendText(const char* obj_name, const char* text);
```
- **功能**: 发送文本到指定控件
- **参数**: obj_name-控件名称, text-文本内容
- **示例**: `TJC_SendText("t_freq", "5.000");`

#### TJC_SendValue()
```c
void TJC_SendValue(const char* obj_name, int value);
```
- **功能**: 发送数值到指定控件
- **参数**: obj_name-控件名称, value-数值
- **示例**: `TJC_SendValue("n_amp", 100);`

#### TJC_SendFloat()
```c
void TJC_SendFloat(const char* obj_name, float value, uint8_t precision);
```
- **功能**: 发送浮点数到指定控件
- **参数**: obj_name-控件名称, value-浮点数, precision-小数位数
- **示例**: `TJC_SendFloat("n_amp", 1.25, 2);` // 发送125

### 高级控制函数

#### TJC_SetFrequency()
```c
uint8_t TJC_SetFrequency(float frequency_mhz);
```
- **功能**: 设置频率并更新显示
- **参数**: frequency_mhz-频率值(MHz)
- **返回**: 0-成功, 1-失败
- **范围**: 0.1MHz - 150MHz

#### TJC_SetAmplitude()
```c
uint8_t TJC_SetAmplitude(float amplitude_v);
```
- **功能**: 设置幅度并更新显示
- **参数**: amplitude_v-幅度值(V)
- **返回**: 0-成功, 1-失败
- **范围**: 0.1V - 3.3V

#### TJC_SetOutput()
```c
void TJC_SetOutput(uint8_t enable);
```
- **功能**: 控制输出开关
- **参数**: enable-1使能, 0禁用

### 系统函数

#### TJC_GotoPage()
```c
void TJC_GotoPage(uint8_t page_id);
```
- **功能**: 跳转到指定页面
- **参数**: page_id-页面ID

#### TJC_InitDisplay()
```c
void TJC_InitDisplay(void);
```
- **功能**: 初始化显示内容
- **说明**: 设置默认参数和界面

## 🎯 使用示例

### 示例1: 基本参数设置
```c
// 设置频率为10.5MHz
TJC_SetFrequency(10.5);

// 设置幅度为2.0V
TJC_SetAmplitude(2.0);

// 开启输出
TJC_SetOutput(1);
```

### 示例2: 手动控件操作
```c
// 显示自定义频率
TJC_SendText("t_freq", "12.345");
TJC_SendText("t_freq_unit", "MHz");

// 显示自定义幅度 (1.23V显示为123)
TJC_SendValue("n_amp", 123);
TJC_SendText("t_amp_unit", "V");

// 显示状态信息
TJC_SendText("t_status", "CUSTOM_MODE");
```

### 示例3: 页面控制
```c
// 跳转到设置页面
TJC_GotoPage(TJC_PAGE_SETTINGS);

// 隐藏状态文本
TJC_SetVisible("t_status", 0);

// 发送系统命令
TJC_SendSystemCommand("rest");  // 重启串口屏
```

## ⚙️ 陶晶池编辑器配置

### 界面设计建议
1. **主页面布局**:
   - 标题区域: 显示设备名称
   - 参数显示区: 频率、幅度数值显示
   - 控制区域: 输出开关、状态指示
   - 导航区域: 页面切换按钮

2. **控件属性设置**:
   - 文本框: 设置合适的字体和颜色
   - 数值框: 设置数值范围和格式
   - 按钮: 设置触摸事件和反馈

3. **事件处理**:
   - 按钮点击: 发送相应的控制命令
   - 数值输入: 实时更新参数
   - 页面切换: 保存当前状态

### 推荐控件配置
```
t_title: 文本框, 字体24, 居中对齐
t_freq: 文本框, 字体32, 右对齐, 数字字体
n_amp: 数值框, 范围10-330, 格式0.00
sw_output: 开关, 样式现代, 颜色绿色/红色
t_status: 文本框, 字体16, 状态颜色
```

## 🔍 调试和故障排除

### 常见问题
1. **串口屏无响应**:
   - 检查波特率设置 (115200)
   - 检查硬件连接
   - 确认电源供应

2. **显示内容错误**:
   - 检查控件名称是否正确
   - 确认命令格式 (结尾\xff\xff\xff)
   - 检查数据类型匹配

3. **触摸无反应**:
   - 检查触摸校准
   - 确认事件代码设置
   - 检查串口接收处理

### 调试方法
1. **串口监控**: 使用串口助手监控通信数据
2. **状态显示**: 通过t_status显示调试信息
3. **分步测试**: 逐个测试各项功能

## 📈 性能优化

### 通信优化
- **批量更新**: 一次性更新多个控件
- **减少频率**: 避免过于频繁的更新
- **缓存机制**: 避免重复发送相同数据

### 显示优化
- **合理布局**: 避免控件重叠和遮挡
- **颜色搭配**: 使用对比度高的颜色
- **字体选择**: 选择清晰易读的字体

## 🎉 集成状态

- ✅ 陶晶池串口屏驱动完成
- ✅ 通信协议实现完成
- ✅ API函数库完成
- ✅ AD9854控制集成完成
- ✅ 编译测试通过
- ✅ 文档完整

**陶晶池串口屏控制模块已成功集成，可立即投入使用！**

## 📞 后续开发建议

1. **界面美化**: 使用陶晶池编辑器设计专业界面
2. **功能扩展**: 添加波形选择、参数预设等功能
3. **数据记录**: 实现参数历史记录和曲线显示
4. **远程控制**: 通过串口屏实现远程参数调整
5. **故障诊断**: 添加系统自检和故障提示功能
