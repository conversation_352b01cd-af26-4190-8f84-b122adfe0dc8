# STM32F4 AD9854串口屏控制集成说明

## 📋 概述

本文档说明如何使用集成的串口屏控制模块来控制AD9854 DDS信号发生器。串口屏控制模块已成功集成到STM32F4项目中，支持实时参数调整和状态显示。

## 🎯 功能特性

### 1. 串口屏控制功能
- ✅ 9600波特率串口通信
- ✅ HMI显示控件支持
- ✅ 实时参数调整
- ✅ 状态信息显示
- ✅ 错误信息提示

### 2. 支持的控制命令
- **'1'** - 设置频率 (演示：切换到6MHz)
- **'2'** - 设置幅度 (演示：切换到0.8V)
- **'3'** - 使能输出
- **'4'** - 禁用输出
- **'5'** - 获取当前状态

### 3. HMI显示控件
- **t0.txt** - 频率显示文本框
- **x0.val** - 幅度显示数值框
- **n0.val** - 状态显示数字框
- **t1.txt** - 错误信息文本框

## 🔧 硬件连接

### 串口屏连接 (USART1)
- **PA9** → 串口屏RX (STM32发送)
- **PA10** → 串口屏TX (STM32接收)
- **GND** → 串口屏GND
- **3.3V** → 串口屏VCC

### AD9854连接 (已有配置)
- 详见项目中的AD9854引脚配置

## 🚀 使用方法

### 1. 初始化
```c
// 在main函数中已自动初始化
if (UartScreen_Init() != 0) {
    Error_Handler();
}
```

### 2. 发送命令到串口屏
```c
// 发送字符串到指定控件
HMI_SendString("t0.txt", "5.000MHz");

// 发送数字到指定控件
HMI_SendNumber("n0.val", 1);

// 发送浮点数到指定控件
HMI_SendFloat("x0.val", 0.5);
```

### 3. 处理串口屏命令
```c
// 在主循环中自动处理
ExternalControl_Process();
```

## 📊 命令协议

### 发送到串口屏的格式
```
控件名="字符串内容"\xff\xff\xff
控件名=数值\xff\xff\xff
```

### 从串口屏接收的格式
```
单字符命令: '1', '2', '3', '4', '5'
```

## 🎮 演示功能

当前实现包含以下演示功能：

1. **频率调整演示**
   - 发送 '1' → 频率切换到6MHz
   - 串口屏显示更新为 "6.000MHz"

2. **幅度调整演示**
   - 发送 '2' → 幅度切换到0.8V
   - 串口屏显示更新为 80 (0.8V * 100)

3. **输出控制演示**
   - 发送 '3' → 使能输出，状态显示为1
   - 发送 '4' → 禁用输出，状态显示为0

4. **状态查询演示**
   - 发送 '5' → 返回当前所有参数状态

## 🔍 调试信息

### 编译状态
- ✅ 编译成功，无错误无警告
- ✅ 程序大小：Code=9796 RO-data=424 RW-data=120 ZI-data=1704

### 模块集成状态
- ✅ external_control.c/h 已添加到项目
- ✅ USART库文件已添加到项目
- ✅ 包含路径已更新
- ✅ 主程序已集成串口屏控制

## 📝 扩展开发

### 1. 添加新的控制命令
在 `UartScreen_ProcessData()` 函数中添加新的case分支：

```c
case '6':  // 新命令
    // 添加处理逻辑
    break;
```

### 2. 添加新的显示控件
在 `external_control.h` 中定义新的控件名称：

```c
#define HMI_NEW_CONTROL "t2.txt"
```

### 3. 自定义参数范围
修改命令处理中的参数值，实现自定义的频率和幅度范围。

## ⚠️ 注意事项

1. **波特率匹配**：确保串口屏设置为9600波特率
2. **控件名称**：HMI控件名称必须与串口屏工程中的控件名称完全一致
3. **数据格式**：浮点数通过乘以100转换为整数发送
4. **结束符**：所有HMI命令必须以 `\xff\xff\xff` 结尾

## 🎯 下一步开发建议

1. **参数输入界面**：在串口屏上添加数字输入界面
2. **波形选择**：添加正弦波、方波、三角波选择
3. **预设参数**：实现参数预设和快速切换
4. **实时监控**：添加输出信号的实时监控显示
5. **参数存储**：实现参数的Flash存储和恢复

## 📞 技术支持

如需进一步的技术支持或功能扩展，请参考：
- STM32F4标准外设库文档
- AD9854数据手册
- 串口屏HMI开发指南
