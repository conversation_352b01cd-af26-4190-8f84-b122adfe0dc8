/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR> 第三问 AD9854 DDS信号发生器
  * @version V1.0
  * @date    2024
  * @brief   STM32F4控制AD9854产生5MHz/0.5V正弦信号
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdint.h>
#include <math.h>

// 核心基础模块
#include "../Modules/Core/systick.h"
#include "bsp.h"

// 第三问专用：AD9854高性能DDS信号生成
#include "../Modules/Generation/ad9854.h"  // AD9854 DDS模块驱动
#include "../Modules/Control/external_control.h"  // 外部控制接口(串口屏)

/* Global variables ----------------------------------------------------------*/
__IO uint32_t uwTick = 0;  ///< 系统滴答计数器

// AD9854配置变量
static AD9854_StatusTypeDef ad9854_status = AD9854_OK;
static AD9854_ControlParams_t control_params;

// AD9854技术优势：
// - 专业DDS芯片，300MHz系统时钟 (20MHz×15倍频)
// - 48位频率分辨率 (0.0018Hz精度)
// - 12位幅度分辨率 (4096级精度)
// - 0Hz-150MHz频率范围，支持高频输出
// - 硬件生成，CPU占用率接近0
// - 目标输出：5MHz正弦波，0.5V峰峰值

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void AD9854_System_Init(void);
static void Error_Handler(void);

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
    /* 系统初始化 */
    SystemClock_Config();
    SysTick_Init();
    BSP_Init();

    /* ==================== 第三问：AD9854外部可控DDS系统 ==================== */
    // 初始化AD9854 DDS模块
    AD9854_System_Init();

    // 初始化串口屏控制接口
    if (UartScreen_Init() != 0) {
        Error_Handler();
    }

    // 初始化外部控制接口
    ad9854_status = AD9854_InitControlInterface(CONTROL_INTERFACE_UART_SCREEN);

    // 启用已知模型电路增益计算 H(s) = 5/(10^-8*s^2 + 3*10^-4*s + 1)
    ad9854_status = AD9854_EnableModelCircuit(1);
    if (ad9854_status != AD9854_OK) Error_Handler();

    // 设置默认参数：5MHz，0.5V峰峰值 (系统将自动计算模型电路增益)
    ad9854_status = AD9854_SetTargetFrequency(5000000.0);  // 5MHz
    if (ad9854_status != AD9854_OK) Error_Handler();

    ad9854_status = AD9854_SetTargetAmplitude(500.0);  // 500mV峰峰值
    if (ad9854_status != AD9854_OK) Error_Handler();

    ad9854_status = AD9854_EnableOutput(1);  // 使能输出
    if (ad9854_status != AD9854_OK) Error_Handler();

    // 等待AD9854输出稳定
    Delay_ms(100);

    // LED快闪3次表示AD9854配置成功
    for (int i = 0; i < 6; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(100);
    }

    /* ==================== AD9854模型电路控制系统就绪！==================== */
    // 当前输出：5MHz正弦波，0.5V峰峰值
    // 模型电路：H(s) = 5/(10^-8*s^2 + 3*10^-4*s + 1)
    // 控制特性：频率可调、峰峰值可调、自动增益补偿、外部接口预留
    // 技术优势：48位频率分辨率、12位幅度控制、频率相关增益补偿

    /* 主循环 - 超稳定5MHz输出 (最小干扰模式) */
    uint32_t led_counter = 0;
    uint32_t stability_check_counter = 0;

    while (1)
    {
        led_counter++;
        stability_check_counter++;

        // ==================== 长期稳定性检查 ====================
        // 每60秒重新确认一次输出，防止长期漂移
        if (stability_check_counter >= 60000000) {  // 约60秒
            // 重新应用当前参数，确保长期稳定性
            AD9854_GetControlParams(&control_params);
            ad9854_status = AD9854_SetTargetFrequency(control_params.frequency_hz);
            if (ad9854_status == AD9854_OK) {
                ad9854_status = AD9854_SetTargetAmplitude(control_params.target_vpp_mv);
            }
            if (ad9854_status != AD9854_OK) {
                Error_Handler();
            }
            stability_check_counter = 0;
        }

        // ==================== 外部控制处理 ====================
        // 处理串口屏命令和矩阵键盘输入
        ExternalControl_Process();

        // ==================== 最小化LED干扰 ====================
        // 大幅降低LED闪烁频率，减少对AD9854的电磁干扰
        if (led_counter % 2000000 == 0) {  // 很慢的闪烁
            if (GPIO_ReadOutputDataBit(GPIOE, GPIO_Pin_6)) {
                GPIO_ResetBits(GPIOE, GPIO_Pin_6);
            } else {
                GPIO_SetBits(GPIOE, GPIO_Pin_6);
            }
        }

        // ==================== 超低功耗模式 ====================
        // 使用WFI指令让CPU进入低功耗状态，减少系统噪声
        __WFI();  // 等待中断，CPU进入睡眠，降低功耗和噪声
    }
}

// ==================== 第三问AD9854配置说明 ====================
//
// 第三问配置：5MHz正弦波输出，峰峰值0.5V
// 目标：高频率、高精度、平滑波形输出
//
// AD9854技术优势：
// 1. 专业DDS芯片，300MHz系统时钟 (20MHz×15倍频)
// 2. 48位频率分辨率，精度达0.0018Hz
// 3. 支持0Hz-150MHz全频率范围
// 4. 12位幅度控制，4096级精度
// 5. 硬件生成，CPU占用率接近0
// 6. 双通道I/Q输出，支持复杂调制
//
// 硬件连接 (嘉立创天空星STM32F407 -> AD9854)：
// PE4  -> RST    (复位信号)
// PE5  -> UDCLK  (更新时钟)
// PE6  -> WR     (写使能)
// PB8  -> RD     (读使能)
// PB9  -> OSK    (OSK控制)
// PD12 -> FSK    (FSK/BPSK/HOLD控制)
// PD0-7 -> D0-D7 (8位数据总线)
// PE8-13 -> A0-A5 (6位地址总线)
//
// 输出信号：AD9854模块的IOUT引脚，5MHz正弦波，0.5V峰峰值
// 波形特性：高频率、低失真、平滑、稳定








/**
  * @brief  系统时钟配置
  * @param  None
  * @retval None
  */
void SystemClock_Config(void)
{
    /* 嘉立创天空星STM32F407VGT6时钟配置 */
    /* 外部晶振：25MHz，目标系统时钟：168MHz */

    RCC_DeInit();

    // 使能HSE
    RCC_HSEConfig(RCC_HSE_ON);
    if (RCC_WaitForHSEStartUp() != SUCCESS) {
        // HSE启动失败，使用HSI
        while(1) {}
    }

    // 配置PLL: HSE = 25MHz, VCO = 25MHz/25*336 = 336MHz, SYSCLK = 336MHz/2 = 168MHz
    RCC_PLLConfig(RCC_PLLSource_HSE, 25, 336, 2, 7);
    RCC_PLLCmd(ENABLE);

    // 等待PLL就绪
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET) {}

    // 配置Flash延迟
    FLASH_SetLatency(FLASH_Latency_5);

    // 配置总线分频
    RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 168MHz
    RCC_PCLK1Config(RCC_HCLK_Div4);    // APB1 = 42MHz
    RCC_PCLK2Config(RCC_HCLK_Div2);    // APB2 = 84MHz

    // 切换到PLL
    RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
    while (RCC_GetSYSCLKSource() != 0x08) {}
}

/**
  * @brief  定时延时递减函数 (SysTick中断调用)
  * @param  None
  * @retval None
  */
void TimingDelay_Decrement(void)
{
    // 这个函数由SysTick中断调用，用于系统延时
    // 实际的延时逻辑已经在SysTick模块中实现
    // 这里保持空实现以满足链接需求
}

/**
 * @brief  AD9854系统初始化
 * @param  None
 * @retval None
 */
void AD9854_System_Init(void)
{
    // 初始化AD9854
    ad9854_status = AD9854_Init();

    if (ad9854_status != AD9854_OK) {
        Error_Handler();
    }

    // 等待初始化完成
    Delay_ms(50);
}

/**
 * @brief  错误处理函数
 * @param  None
 * @retval None
 */
static void Error_Handler(void)
{
    // 错误指示：LED快速闪烁
    while (1) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(50);  // 快速闪烁表示错误
    }
}

/**
  * @brief  断言失败处理函数
  * @param  file: 源文件名
  * @param  line: 行号
  * @retval None
  */
#ifdef USE_FULL_ASSERT
void assert_failed(uint8_t* file, uint32_t line)
{
    /* 用户可在此添加记录或打印功能，这里简单死循环 */
    while (1) {}
}
#endif

// DDS_TIM6_IRQHandler_Internal函数已在dds_wavegen.c中实现，这里删除重复定义

/**
  * @brief  EXTI0中断处理函数内部实现 (空实现)
  * @param  None
  * @retval None
  */
void EXTI0_IRQHandler_Internal(void)
{
    // 空实现，因为我们不使用外部中断
    // 第二问只需要DAC输出正弦波
}


