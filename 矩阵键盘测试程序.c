/**
  ******************************************************************************
  * @file    矩阵键盘测试程序.c
  * <AUTHOR> 第三问 4x4矩阵键盘测试
  * @version V1.0
  * @date    2024
  * @brief   4x4矩阵键盘控制功能测试程序
  ******************************************************************************
  * @attention
  * 
  * 本文件提供4x4矩阵键盘控制功能的测试代码
  * 可以独立运行，用于验证矩阵键盘功能是否正常
  * 
  ******************************************************************************
  */

/* 测试说明：
 * 1. 将此代码添加到main.c中的适当位置进行测试
 * 2. 或者创建独立的测试函数调用
 * 3. 通过串口屏观察按键响应和参数设置结果
 */

#include "main.h"
#include "../Modules/Control/external_control.h"
#include "../Modules/Core/systick.h"

/**
 * @brief  矩阵键盘基础功能测试
 * @param  None
 * @retval None
 */
void MatrixKeypad_BasicTest(void)
{
    uint8_t key;
    uint32_t test_count = 0;
    
    // 发送测试开始信息
    HMI_SendString("t0.txt", "KEY_TEST");
    HMI_SendString("t1.txt", "PRESS_KEY");
    
    while (test_count < 50) {  // 测试50次按键
        key = MatrixKeypad_Scan();
        
        if (key != KEY_NONE) {
            // 显示按键信息
            char key_info[16];
            snprintf(key_info, sizeof(key_info), "KEY:%c", MatrixKeypad_GetKeyChar(key));
            HMI_SendString("t1.txt", key_info);
            
            // 处理按键
            MatrixKeypad_ProcessKey(key);
            
            test_count++;
            
            // 等待按键释放
            Delay_ms(200);
        }
        
        Delay_ms(10);
    }
    
    HMI_SendString("t1.txt", "TEST_DONE");
}

/**
 * @brief  矩阵键盘频率设置测试
 * @param  None
 * @retval None
 */
void MatrixKeypad_FrequencyTest(void)
{
    // 测试频率设置序列
    uint8_t freq_test_sequence[] = {
        KEY_A,      // 进入频率模式
        KEY_5,      // 输入5
        KEY_HASH,   // 小数点
        KEY_0,      // 输入0
        KEY_C       // 确认
    };
    
    HMI_SendString("t0.txt", "FREQ_TEST");
    HMI_SendString("t1.txt", "AUTO_TEST");
    
    for (int i = 0; i < sizeof(freq_test_sequence); i++) {
        // 显示当前按键
        char key_info[16];
        snprintf(key_info, sizeof(key_info), "AUTO:%c", 
                MatrixKeypad_GetKeyChar(freq_test_sequence[i]));
        HMI_SendString("t1.txt", key_info);
        
        // 处理按键
        MatrixKeypad_ProcessKey(freq_test_sequence[i]);
        
        Delay_ms(1000);  // 延时观察效果
    }
    
    HMI_SendString("t1.txt", "FREQ_OK");
}

/**
 * @brief  矩阵键盘幅度设置测试
 * @param  None
 * @retval None
 */
void MatrixKeypad_AmplitudeTest(void)
{
    // 测试幅度设置序列
    uint8_t amp_test_sequence[] = {
        KEY_B,      // 进入幅度模式
        KEY_1,      // 输入1
        KEY_HASH,   // 小数点
        KEY_2,      // 输入2
        KEY_C       // 确认
    };
    
    HMI_SendString("t0.txt", "AMP_TEST");
    HMI_SendString("t1.txt", "AUTO_TEST");
    
    for (int i = 0; i < sizeof(amp_test_sequence); i++) {
        // 显示当前按键
        char key_info[16];
        snprintf(key_info, sizeof(key_info), "AUTO:%c", 
                MatrixKeypad_GetKeyChar(amp_test_sequence[i]));
        HMI_SendString("t1.txt", key_info);
        
        // 处理按键
        MatrixKeypad_ProcessKey(amp_test_sequence[i]);
        
        Delay_ms(1000);  // 延时观察效果
    }
    
    HMI_SendString("t1.txt", "AMP_OK");
}

/**
 * @brief  矩阵键盘输出控制测试
 * @param  None
 * @retval None
 */
void MatrixKeypad_OutputTest(void)
{
    HMI_SendString("t0.txt", "OUT_TEST");
    
    // 测试输出开关
    for (int i = 0; i < 5; i++) {
        HMI_SendString("t1.txt", "TOGGLE");
        MatrixKeypad_ProcessKey(KEY_D);  // 切换输出
        Delay_ms(2000);
    }
    
    HMI_SendString("t1.txt", "OUT_OK");
}

/**
 * @brief  矩阵键盘错误处理测试
 * @param  None
 * @retval None
 */
void MatrixKeypad_ErrorTest(void)
{
    HMI_SendString("t0.txt", "ERR_TEST");
    
    // 测试超出范围的频率
    uint8_t error_freq_sequence[] = {
        KEY_A,      // 进入频率模式
        KEY_2,      // 输入2
        KEY_0,      // 输入0
        KEY_0,      // 输入0 (200MHz，超出范围)
        KEY_C       // 确认 (应该显示错误)
    };
    
    for (int i = 0; i < sizeof(error_freq_sequence); i++) {
        MatrixKeypad_ProcessKey(error_freq_sequence[i]);
        Delay_ms(500);
    }
    
    Delay_ms(2000);
    
    // 测试清除功能
    MatrixKeypad_ProcessKey(KEY_STAR);  // 清除
    Delay_ms(1000);
    
    HMI_SendString("t1.txt", "ERR_OK");
}

/**
 * @brief  矩阵键盘综合测试主函数
 * @param  None
 * @retval None
 * @note   在main函数中调用此函数进行测试
 */
void MatrixKeypad_MainTest(void)
{
    // 初始化矩阵键盘
    if (MatrixKeypad_Init() != 0) {
        // 初始化失败，LED快速闪烁
        while (1) {
            GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
            Delay_ms(100);
        }
    }
    
    // 初始化串口屏
    if (UartScreen_Init() != 0) {
        // 初始化失败
        while (1) {
            GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
            Delay_ms(200);
        }
    }
    
    // LED慢闪表示初始化成功
    for (int i = 0; i < 6; i++) {
        GPIO_ToggleBits(GPIOE, GPIO_Pin_6);
        Delay_ms(300);
    }
    
    // 发送欢迎信息
    HMI_SendString("t0.txt", "KEYPAD_READY");
    HMI_SendString("t1.txt", "TEST_START");
    HMI_SendNumber("n0.val", 0);
    HMI_SendFloat("x0.val", 0.0);
    
    Delay_ms(2000);
    
    // 执行各项测试
    
    // 1. 基础按键测试
    MatrixKeypad_BasicTest();
    Delay_ms(2000);
    
    // 2. 频率设置测试
    MatrixKeypad_FrequencyTest();
    Delay_ms(2000);
    
    // 3. 幅度设置测试
    MatrixKeypad_AmplitudeTest();
    Delay_ms(2000);
    
    // 4. 输出控制测试
    MatrixKeypad_OutputTest();
    Delay_ms(2000);
    
    // 5. 错误处理测试
    MatrixKeypad_ErrorTest();
    Delay_ms(2000);
    
    // 发送测试完成信息
    HMI_SendString("t0.txt", "ALL_TESTS");
    HMI_SendString("t1.txt", "COMPLETED");
    
    // 进入正常工作模式
    while (1) {
        // 处理矩阵键盘和串口屏
        ExternalControl_Process();
        
        // 定期发送心跳信息
        static uint32_t heartbeat_counter = 0;
        heartbeat_counter++;
        
        if (heartbeat_counter % 1000000 == 0) {
            HMI_SendNumber("n0.val", heartbeat_counter / 1000000);
        }
        
        Delay_ms(1);
    }
}

/* 使用方法：
 * 
 * 方法1：在main函数中直接调用测试
 * int main(void) {
 *     SystemClock_Config();
 *     SysTick_Init();
 *     BSP_Init();
 *     
 *     MatrixKeypad_MainTest();  // 调用测试函数
 * }
 * 
 * 方法2：在现有main函数中添加测试代码
 * 将各个测试函数添加到适当的位置进行测试
 * 
 * 方法3：手动测试
 * 1. 连接4x4矩阵键盘到PC0-PC7
 * 2. 连接串口屏观察显示
 * 3. 按键测试各种功能
 * 4. 观察串口屏的反馈信息
 * 
 * 测试步骤：
 * 1. 按[A]进入频率模式，输入数字，按[C]确认
 * 2. 按[B]进入幅度模式，输入数字，按[C]确认
 * 3. 按[D]切换输出开关
 * 4. 按[*]清除输入
 * 5. 按[#]添加小数点
 */
