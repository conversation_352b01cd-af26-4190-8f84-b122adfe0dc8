/**
  ******************************************************************************
  * @file    external_control.c
  * <AUTHOR> 第三问 外部控制接口
  * @version V1.0
  * @date    2024
  * @brief   AD9854外部控制接口实现 - 支持串口屏和矩阵键盘
  ******************************************************************************
  * @attention
  * 
  * 本文件提供AD9854外部控制的基础框架实现
  * 具体的串口屏和矩阵键盘功能将根据实际硬件进行完善
  * 
  * 当前实现：
  * - 基础框架和接口定义
  * - 参数解析和格式化函数
  * - 预留硬件接口初始化
  * 
  * 待完善功能：
  * - 串口屏通信协议
  * - 矩阵键盘扫描算法
  * - Flash参数存储
  * 
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "external_control.h"
#include "../Core/usart.h"
#include "../Generation/ad9854.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// 控制结构体实例
static UartScreen_Control_t uart_screen_ctrl;
static MatrixKeypad_Control_t keypad_ctrl;

// 预设配置 (示例)
static ParamPreset_t default_presets[MAX_PRESETS] = {
    {"5MHz_500mV", 5000000.0, 500.0, 1.0, 1},
    {"1MHz_1V", 1000000.0, 1000.0, 1.0, 1},
    {"10MHz_250mV", 10000000.0, 250.0, 1.0, 1},
    {"100kHz_2V", 100000.0, 2000.0, 1.0, 1},
    {"", 0, 0, 0, 0},  // 空预设
    {"", 0, 0, 0, 0},
    {"", 0, 0, 0, 0},
    {"", 0, 0, 0, 0}
};

/* Private function prototypes -----------------------------------------------*/
static double parse_number_with_unit(const char *str, const char *unit);
static void format_number_with_unit(double value, const char *unit, char *buffer, uint8_t buffer_size);

/* Exported functions --------------------------------------------------------*/

// ==================== 串口屏控制函数实现 ====================

/**
 * @brief  初始化串口屏控制
 * @param  None
 * @retval uint8_t 初始化状态 (0-成功, 1-失败)
 */
uint8_t UartScreen_Init(void)
{
    // 清空控制结构体
    memset(&uart_screen_ctrl, 0, sizeof(uart_screen_ctrl));
    uart_screen_ctrl.current_state = UI_STATE_MAIN_MENU;

    // 初始化UART硬件 (使用已有的USART模块)
    if (USART1_Init(UART_SCREEN_BAUDRATE) != 0) {
        return 1;  // 初始化失败
    }

    uart_screen_ctrl.initialized = 1;

    // 发送初始化信息到串口屏
    HMI_SendString(HMI_FREQ_TEXT, "5.000MHz");
    HMI_SendFloat(HMI_AMP_VALUE, 0.5);
    HMI_SendNumber(HMI_STATUS_NUM, 1);

    return 0;  // 成功
}

/**
 * @brief  处理串口屏接收数据
 * @param  None
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t UartScreen_ProcessData(void)
{
    if (!uart_screen_ctrl.initialized) {
        return 0;
    }

    // 检查是否有数据接收完成 (模拟USART_RX_STA机制)
    uint8_t received_data;
    if (USART_ReceiveByte(&received_data, 0) == 0) {  // 非阻塞接收
        // 处理接收到的命令
        switch (received_data) {
            case UART_CMD_SET_FREQ:  // '1' - 设置频率
                HMI_SendString(HMI_FREQ_TEXT, "6.000MHz");
                AD9854_SetTargetFrequency(6000000.0);  // 设置为6MHz
                break;

            case UART_CMD_SET_AMP:   // '2' - 设置幅度
                HMI_SendFloat(HMI_AMP_VALUE, 0.8);
                AD9854_SetTargetAmplitude(800.0);  // 设置为800mV
                break;

            case UART_CMD_ENABLE:    // '3' - 使能输出
                HMI_SendNumber(HMI_STATUS_NUM, 1);
                AD9854_EnableOutput(1);
                break;

            case UART_CMD_DISABLE:   // '4' - 禁用输出
                HMI_SendNumber(HMI_STATUS_NUM, 0);
                AD9854_EnableOutput(0);
                break;

            case UART_CMD_GET_STATUS: // '5' - 获取状态
                UartScreen_SendCurrentStatus();
                break;

            default:
                // 未知命令，忽略
                break;
        }
        return 1;  // 有命令处理
    }

    return 0;  // 无命令
}

/**
 * @brief  发送状态信息到串口屏
 * @param  params: 当前参数
 * @retval None
 */
void UartScreen_SendStatus(const AD9854_ControlParams_t *params)
{
    if (!uart_screen_ctrl.initialized || params == NULL) {
        return;
    }

    // 格式化并发送频率信息
    char freq_str[16];
    snprintf(freq_str, sizeof(freq_str), "%.3fMHz", params->frequency_hz / 1000000.0);
    HMI_SendString(HMI_FREQ_TEXT, freq_str);

    // 发送幅度信息
    HMI_SendFloat(HMI_AMP_VALUE, params->target_vpp_mv / 1000.0);

    // 发送使能状态
    HMI_SendNumber(HMI_STATUS_NUM, params->output_enabled ? 1 : 0);
}

/**
 * @brief  发送错误信息到串口屏
 * @param  error_code: 错误代码
 * @retval None
 */
void UartScreen_SendError(uint8_t error_code)
{
    if (!uart_screen_ctrl.initialized) {
        return;
    }

    char error_str[16];
    snprintf(error_str, sizeof(error_str), "ERROR:%02d", error_code);
    HMI_SendString(HMI_ERROR_TEXT, error_str);
}

/**
 * @brief  发送当前状态到串口屏
 * @param  None
 * @retval None
 */
void UartScreen_SendCurrentStatus(void)
{
    AD9854_ControlParams_t current_params;
    if (AD9854_GetControlParams(&current_params) == AD9854_OK) {
        UartScreen_SendStatus(&current_params);
    }
}

// ==================== 矩阵键盘控制函数实现 ====================

/**
 * @brief  初始化矩阵键盘
 * @param  None
 * @retval uint8_t 初始化状态 (0-成功, 1-失败)
 */
uint8_t MatrixKeypad_Init(void)
{
    // 清空控制结构体
    memset(&keypad_ctrl, 0, sizeof(keypad_ctrl));
    keypad_ctrl.current_menu = UI_STATE_MAIN_MENU;
    keypad_ctrl.last_key = 0xFF;
    keypad_ctrl.current_key = 0xFF;
    
    // TODO: 初始化GPIO引脚
    // 行引脚配置为输出，列引脚配置为输入上拉
    
    return 0;  // 暂时返回成功
}

/**
 * @brief  扫描矩阵键盘
 * @param  None
 * @retval uint8_t 按键值 (0xFF-无按键)
 */
uint8_t MatrixKeypad_Scan(void)
{
    // TODO: 实现4x4矩阵键盘扫描
    // 逐行扫描，检测列输入状态
    
    return 0xFF;  // 暂时返回无按键
}

/**
 * @brief  处理键盘输入
 * @param  key: 按键值
 * @retval uint8_t 处理状态 (0-继续输入, 1-命令完成)
 */
uint8_t MatrixKeypad_ProcessKey(uint8_t key)
{
    // TODO: 根据当前菜单状态处理按键
    // 数字键用于参数输入，功能键用于菜单导航
    
    return 0;  // 暂时返回继续输入
}

/**
 * @brief  显示当前菜单 (通过LED或其他方式)
 * @param  menu_state: 菜单状态
 * @retval None
 */
void MatrixKeypad_DisplayMenu(UI_State_t menu_state)
{
    // TODO: 通过LED或其他方式显示当前菜单状态
    // 可以使用不同的LED闪烁模式表示不同菜单
}

// ==================== 参数存储函数实现 ====================

/**
 * @brief  保存参数到Flash
 * @param  preset_index: 预设索引 (0-7)
 * @param  params: 参数结构体
 * @retval uint8_t 保存状态 (0-成功, 1-失败)
 */
uint8_t ParamStorage_Save(uint8_t preset_index, const AD9854_ControlParams_t *params)
{
    // TODO: 实现Flash存储
    // 将参数保存到指定的Flash扇区
    
    return 0;  // 暂时返回成功
}

/**
 * @brief  从Flash加载参数
 * @param  preset_index: 预设索引 (0-7)
 * @param  params: 参数结构体指针
 * @retval uint8_t 加载状态 (0-成功, 1-失败)
 */
uint8_t ParamStorage_Load(uint8_t preset_index, AD9854_ControlParams_t *params)
{
    // TODO: 实现Flash读取
    // 从指定的Flash扇区读取参数
    
    return 1;  // 暂时返回失败，使用默认参数
}

/**
 * @brief  获取预设配置列表
 * @param  presets: 预设配置数组
 * @retval uint8_t 有效预设数量
 */
uint8_t ParamStorage_GetPresets(ParamPreset_t presets[MAX_PRESETS])
{
    uint8_t count = 0;
    
    // 复制默认预设
    for (int i = 0; i < MAX_PRESETS; i++) {
        presets[i] = default_presets[i];
        if (strlen(default_presets[i].name) > 0) {
            count++;
        }
    }
    
    return count;
}

// ==================== 通用控制函数实现 ====================

/**
 * @brief  外部控制主循环处理
 * @param  None
 * @retval None
 */
void ExternalControl_Process(void)
{
    // 处理串口屏数据
    UartScreen_ProcessData();

    // 扫描矩阵键盘
    uint8_t key = MatrixKeypad_Scan();
    if (key != 0xFF) {
        MatrixKeypad_ProcessKey(key);
    }
}

// ==================== HMI通信函数实现 ====================

/**
 * @brief  HMI发送字符串到指定控件
 * @param  name: 控件名称
 * @param  showdata: 要显示的字符串
 * @retval None
 */
void HMI_SendString(const char* name, const char* showdata)
{
    if (name == NULL || showdata == NULL) {
        return;
    }

    // 格式: name="showdata"\xff\xff\xff
    USART_Printf("%s=\"%s\"\xff\xff\xff", name, showdata);
}

/**
 * @brief  HMI发送数字到指定控件
 * @param  name: 控件名称
 * @param  num: 要显示的数字
 * @retval None
 */
void HMI_SendNumber(const char* name, int num)
{
    if (name == NULL) {
        return;
    }

    // 格式: name=num\xff\xff\xff
    USART_Printf("%s=%d\xff\xff\xff", name, num);
}

/**
 * @brief  HMI发送浮点数到指定控件
 * @param  name: 控件名称
 * @param  num: 要显示的浮点数
 * @retval None
 */
void HMI_SendFloat(const char* name, float num)
{
    if (name == NULL) {
        return;
    }

    // 格式: name=(int)(num*100)\xff\xff\xff (浮点数转换为整数发送)
    USART_Printf("%s=%d\xff\xff\xff", name, (int)(num * 100));
}

/**
 * @brief  解析频率字符串
 * @param  freq_str: 频率字符串 (如 "5.5MHz", "1000Hz")
 * @retval double 频率值 (Hz)，-1表示解析失败
 */
double ExternalControl_ParseFrequency(const char *freq_str)
{
    double freq = -1;
    
    if (strstr(freq_str, "MHz") || strstr(freq_str, "mhz")) {
        freq = parse_number_with_unit(freq_str, "MHz") * 1000000.0;
    } else if (strstr(freq_str, "kHz") || strstr(freq_str, "khz")) {
        freq = parse_number_with_unit(freq_str, "kHz") * 1000.0;
    } else if (strstr(freq_str, "Hz") || strstr(freq_str, "hz")) {
        freq = parse_number_with_unit(freq_str, "Hz");
    } else {
        // 默认单位为Hz
        freq = atof(freq_str);
    }
    
    return freq;
}

/**
 * @brief  解析幅度字符串
 * @param  amp_str: 幅度字符串 (如 "500mV", "1.5V")
 * @retval double 幅度值 (mV)，-1表示解析失败
 */
double ExternalControl_ParseAmplitude(const char *amp_str)
{
    double amp = -1;
    
    if (strstr(amp_str, "V") && !strstr(amp_str, "mV")) {
        amp = parse_number_with_unit(amp_str, "V") * 1000.0;  // V转mV
    } else if (strstr(amp_str, "mV") || strstr(amp_str, "mv")) {
        amp = parse_number_with_unit(amp_str, "mV");
    } else {
        // 默认单位为mV
        amp = atof(amp_str);
    }
    
    return amp;
}

/**
 * @brief  格式化频率显示
 * @param  frequency_hz: 频率 (Hz)
 * @param  buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval None
 */
void ExternalControl_FormatFrequency(double frequency_hz, char *buffer, uint8_t buffer_size)
{
    if (frequency_hz >= 1000000.0) {
        format_number_with_unit(frequency_hz / 1000000.0, "MHz", buffer, buffer_size);
    } else if (frequency_hz >= 1000.0) {
        format_number_with_unit(frequency_hz / 1000.0, "kHz", buffer, buffer_size);
    } else {
        format_number_with_unit(frequency_hz, "Hz", buffer, buffer_size);
    }
}

/**
 * @brief  格式化幅度显示
 * @param  amplitude_mv: 幅度 (mV)
 * @param  buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval None
 */
void ExternalControl_FormatAmplitude(double amplitude_mv, char *buffer, uint8_t buffer_size)
{
    if (amplitude_mv >= 1000.0) {
        format_number_with_unit(amplitude_mv / 1000.0, "V", buffer, buffer_size);
    } else {
        format_number_with_unit(amplitude_mv, "mV", buffer, buffer_size);
    }
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  解析带单位的数字字符串
 * @param  str: 输入字符串
 * @param  unit: 单位字符串
 * @retval double 数值部分
 */
static double parse_number_with_unit(const char *str, const char *unit)
{
    char *unit_pos = strstr(str, unit);
    if (unit_pos == NULL) {
        return atof(str);
    }
    
    // 创建临时字符串，只包含数字部分
    char temp_str[32];
    int len = unit_pos - str;
    if (len >= sizeof(temp_str)) {
        len = sizeof(temp_str) - 1;
    }
    strncpy(temp_str, str, len);
    temp_str[len] = '\0';
    
    return atof(temp_str);
}

/**
 * @brief  格式化带单位的数字
 * @param  value: 数值
 * @param  unit: 单位
 * @param  buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval None
 */
static void format_number_with_unit(double value, const char *unit, char *buffer, uint8_t buffer_size)
{
    snprintf(buffer, buffer_size, "%.3f%s", value, unit);
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
