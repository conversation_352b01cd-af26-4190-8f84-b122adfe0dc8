/**
  ******************************************************************************
  * @file    external_control.c
  * <AUTHOR> 第三问 外部控制接口
  * @version V1.0
  * @date    2024
  * @brief   AD9854外部控制接口实现 - 支持串口屏和矩阵键盘
  ******************************************************************************
  * @attention
  * 
  * 本文件提供AD9854外部控制的基础框架实现
  * 具体的串口屏和矩阵键盘功能将根据实际硬件进行完善
  * 
  * 当前实现：
  * - 基础框架和接口定义
  * - 参数解析和格式化函数
  * - 预留硬件接口初始化
  * 
  * 待完善功能：
  * - 串口屏通信协议
  * - 矩阵键盘扫描算法
  * - Flash参数存储
  * 
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "external_control.h"
#include "../Core/usart.h"
#include "../Generation/ad9854.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

// 陶晶池串口屏控制结构体实例
static TJC_Screen_Control_t tjc_screen_ctrl;



/* Private function prototypes -----------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

// ==================== 陶晶池串口屏控制函数实现 ====================

/**
 * @brief  初始化陶晶池串口屏控制
 * @param  None
 * @retval uint8_t 初始化状态 (0-成功, 1-失败)
 */
uint8_t TJC_Screen_Init(void)
{
    // 清空控制结构体
    memset(&tjc_screen_ctrl, 0, sizeof(tjc_screen_ctrl));
    tjc_screen_ctrl.current_page = TJC_PAGE_MAIN;

    // 初始化UART硬件 (使用已有的USART模块)
    if (USART1_Init(TJC_SCREEN_BAUDRATE) != 0) {
        return 1;  // 初始化失败
    }

    tjc_screen_ctrl.initialized = 1;

    // 延时等待串口屏启动
    Delay_ms(1000);

    // 初始化显示
    TJC_InitDisplay();

    return 0;  // 成功
}

/**
 * @brief  处理串口屏接收数据
 * @param  None
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t UartScreen_ProcessData(void)
{
    if (!uart_screen_ctrl.initialized) {
        return 0;
    }

    // 检查是否有数据接收完成 (模拟USART_RX_STA机制)
    uint8_t received_data;
    if (USART_ReceiveByte(&received_data, 0) == 0) {  // 非阻塞接收
        // 处理接收到的命令
        switch (received_data) {
            case UART_CMD_SET_FREQ:  // '1' - 设置频率
                HMI_SendString(HMI_FREQ_TEXT, "6.000MHz");
                AD9854_SetTargetFrequency(6000000.0);  // 设置为6MHz
                break;

            case UART_CMD_SET_AMP:   // '2' - 设置幅度
                HMI_SendFloat(HMI_AMP_VALUE, 0.8);
                AD9854_SetTargetAmplitude(800.0);  // 设置为800mV
                break;

            case UART_CMD_ENABLE:    // '3' - 使能输出
                HMI_SendNumber(HMI_STATUS_NUM, 1);
                AD9854_EnableOutput(1);
                break;

            case UART_CMD_DISABLE:   // '4' - 禁用输出
                HMI_SendNumber(HMI_STATUS_NUM, 0);
                AD9854_EnableOutput(0);
                break;

            case UART_CMD_GET_STATUS: // '5' - 获取状态
                UartScreen_SendCurrentStatus();
                break;

            default:
                // 未知命令，忽略
                break;
        }
        return 1;  // 有命令处理
    }

    return 0;  // 无命令
}

/**
 * @brief  发送状态信息到串口屏
 * @param  params: 当前参数
 * @retval None
 */
void UartScreen_SendStatus(const AD9854_ControlParams_t *params)
{
    if (!uart_screen_ctrl.initialized || params == NULL) {
        return;
    }

    // 格式化并发送频率信息
    char freq_str[16];
    snprintf(freq_str, sizeof(freq_str), "%.3fMHz", params->frequency_hz / 1000000.0);
    HMI_SendString(HMI_FREQ_TEXT, freq_str);

    // 发送幅度信息
    HMI_SendFloat(HMI_AMP_VALUE, params->target_vpp_mv / 1000.0);

    // 发送使能状态
    HMI_SendNumber(HMI_STATUS_NUM, params->output_enabled ? 1 : 0);
}

/**
 * @brief  发送错误信息到串口屏
 * @param  error_code: 错误代码
 * @retval None
 */
void UartScreen_SendError(uint8_t error_code)
{
    if (!uart_screen_ctrl.initialized) {
        return;
    }

    char error_str[16];
    snprintf(error_str, sizeof(error_str), "ERROR:%02d", error_code);
    HMI_SendString(HMI_ERROR_TEXT, error_str);
}

/**
 * @brief  发送当前状态到串口屏
 * @param  None
 * @retval None
 */
void UartScreen_SendCurrentStatus(void)
{
    AD9854_ControlParams_t current_params;
    if (AD9854_GetControlParams(&current_params) == AD9854_OK) {
        UartScreen_SendStatus(&current_params);
    }
}

// ==================== 陶晶池串口屏控制函数实现 ====================







// ==================== 参数存储函数实现 ====================

/**
 * @brief  保存参数到Flash
 * @param  preset_index: 预设索引 (0-7)
 * @param  params: 参数结构体
 * @retval uint8_t 保存状态 (0-成功, 1-失败)
 */
uint8_t ParamStorage_Save(uint8_t preset_index, const AD9854_ControlParams_t *params)
{
    // TODO: 实现Flash存储
    // 将参数保存到指定的Flash扇区
    
    return 0;  // 暂时返回成功
}

/**
 * @brief  从Flash加载参数
 * @param  preset_index: 预设索引 (0-7)
 * @param  params: 参数结构体指针
 * @retval uint8_t 加载状态 (0-成功, 1-失败)
 */
uint8_t ParamStorage_Load(uint8_t preset_index, AD9854_ControlParams_t *params)
{
    // TODO: 实现Flash读取
    // 从指定的Flash扇区读取参数
    
    return 1;  // 暂时返回失败，使用默认参数
}

/**
 * @brief  获取预设配置列表
 * @param  presets: 预设配置数组
 * @retval uint8_t 有效预设数量
 */
uint8_t ParamStorage_GetPresets(ParamPreset_t presets[MAX_PRESETS])
{
    uint8_t count = 0;
    
    // 复制默认预设
    for (int i = 0; i < MAX_PRESETS; i++) {
        presets[i] = default_presets[i];
        if (strlen(default_presets[i].name) > 0) {
            count++;
        }
    }
    
    return count;
}

// ==================== 通用控制函数实现 ====================

/**
 * @brief  外部控制主循环处理
 * @param  None
 * @retval None
 */
void ExternalControl_Process(void)
{
    // 处理陶晶池串口屏数据
    TJC_Screen_ProcessData();
}

// ==================== HMI通信函数实现 ====================

/**
 * @brief  HMI发送字符串到指定控件
 * @param  name: 控件名称
 * @param  showdata: 要显示的字符串
 * @retval None
 */
void HMI_SendString(const char* name, const char* showdata)
{
    if (name == NULL || showdata == NULL) {
        return;
    }

    // 格式: name="showdata"\xff\xff\xff
    USART_Printf("%s=\"%s\"\xff\xff\xff", name, showdata);
}

/**
 * @brief  HMI发送数字到指定控件
 * @param  name: 控件名称
 * @param  num: 要显示的数字
 * @retval None
 */
void HMI_SendNumber(const char* name, int num)
{
    if (name == NULL) {
        return;
    }

    // 格式: name=num\xff\xff\xff
    USART_Printf("%s=%d\xff\xff\xff", name, num);
}

/**
 * @brief  HMI发送浮点数到指定控件
 * @param  name: 控件名称
 * @param  num: 要显示的浮点数
 * @retval None
 */
void HMI_SendFloat(const char* name, float num)
{
    if (name == NULL) {
        return;
    }

    // 格式: name=(int)(num*100)\xff\xff\xff (浮点数转换为整数发送)
    USART_Printf("%s=%d\xff\xff\xff", name, (int)(num * 100));
}

// ==================== 陶晶池串口屏专用函数实现 ====================





/**
 * @brief  陶晶池串口屏数据处理
 * @param  None
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t TJC_Screen_ProcessData(void)
{
    if (!tjc_screen_ctrl.initialized) {
        return 0;
    }

    // 检查是否有数据接收完成
    uint8_t received_data;
    if (USART_ReceiveByte(&received_data, 0) == 0) {  // 非阻塞接收
        // 处理陶晶池串口屏的数据包
        return TJC_ParseCommand(&received_data, 1);
    }

    return 0;  // 无命令
}

/**
 * @brief  陶晶池串口屏命令解析
 * @param  data: 接收到的数据
 * @param  length: 数据长度
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t TJC_ParseCommand(uint8_t *data, uint8_t length)
{
    // 陶晶池串口屏的数据包格式通常以0xFF 0xFF 0xFF结尾
    // 这里实现基本的命令解析逻辑

    // TODO: 根据实际的陶晶池串口屏协议实现
    return 0;
}

/**
 * @brief  陶晶池串口屏发送文本到指定控件
 * @param  obj_name: 控件名称
 * @param  text: 要显示的文本
 * @retval None
 */
void TJC_SendText(const char* obj_name, const char* text)
{
    if (obj_name == NULL || text == NULL) {
        return;
    }

    // 陶晶池串口屏文本赋值格式: obj_name.txt="text"
    USART_Printf("%s.txt=\"%s\"\xff\xff\xff", obj_name, text);
}

/**
 * @brief  陶晶池串口屏发送数值到指定控件
 * @param  obj_name: 控件名称
 * @param  value: 要显示的数值
 * @retval None
 */
void TJC_SendValue(const char* obj_name, int value)
{
    if (obj_name == NULL) {
        return;
    }

    // 陶晶池串口屏数值赋值格式: obj_name.val=value
    USART_Printf("%s.val=%d\xff\xff\xff", obj_name, value);
}

/**
 * @brief  陶晶池串口屏发送浮点数到指定控件
 * @param  obj_name: 控件名称
 * @param  value: 要显示的浮点数
 * @param  precision: 小数位数
 * @retval None
 */
void TJC_SendFloat(const char* obj_name, float value, uint8_t precision)
{
    if (obj_name == NULL) {
        return;
    }

    // 将浮点数转换为整数发送 (乘以10^precision)
    int multiplier = 1;
    for (uint8_t i = 0; i < precision; i++) {
        multiplier *= 10;
    }

    int int_value = (int)(value * multiplier);
    USART_Printf("%s.val=%d\xff\xff\xff", obj_name, int_value);
}

/**
 * @brief  陶晶池串口屏设置频率
 * @param  frequency_mhz: 频率值 (MHz)
 * @retval uint8_t 设置状态 (0-成功, 1-失败)
 */
uint8_t TJC_SetFrequency(float frequency_mhz)
{
    // 频率范围检查 (0.1MHz - 150MHz)
    if (frequency_mhz < 0.1 || frequency_mhz > 150.0) {
        TJC_SendText("t_status", "FREQ_ERR");
        return 1;  // 失败
    }

    // 设置AD9854频率
    AD9854_SetTargetFrequency(frequency_mhz * 1000000.0);  // 转换为Hz

    // 更新串口屏显示
    char freq_str[16];
    snprintf(freq_str, sizeof(freq_str), "%.3f", frequency_mhz);
    TJC_SendText("t_freq", freq_str);
    TJC_SendText("t_status", "FREQ_OK");

    return 0;  // 成功
}

/**
 * @brief  陶晶池串口屏设置幅度
 * @param  amplitude_v: 幅度值 (V)
 * @retval uint8_t 设置状态 (0-成功, 1-失败)
 */
uint8_t TJC_SetAmplitude(float amplitude_v)
{
    // 幅度范围检查 (0.1V - 3.3V)
    if (amplitude_v < 0.1 || amplitude_v > 3.3) {
        TJC_SendText("t_status", "AMP_ERR");
        return 1;  // 失败
    }

    // 设置AD9854幅度
    AD9854_SetTargetAmplitude(amplitude_v * 1000.0);  // 转换为mV

    // 更新串口屏显示
    TJC_SendFloat("n_amp", amplitude_v, 2);  // 显示2位小数
    TJC_SendText("t_status", "AMP_OK");

    return 0;  // 成功
}

/**
 * @brief  陶晶池串口屏切换输出开关
 * @param  enable: 1-使能, 0-禁用
 * @retval None
 */
void TJC_SetOutput(uint8_t enable)
{
    AD9854_EnableOutput(enable);

    // 更新串口屏显示
    TJC_SendValue("sw_output", enable);
    TJC_SendText("t_status", enable ? "OUT_ON" : "OUT_OFF");
}

/**
 * @brief  陶晶池串口屏更新所有显示
 * @param  None
 * @retval None
 */
void TJC_UpdateAllDisplay(void)
{
    AD9854_ControlParams_t current_params;
    if (AD9854_GetControlParams(&current_params) == AD9854_OK) {
        // 更新频率显示
        float freq_mhz = current_params.frequency_hz / 1000000.0;
        char freq_str[16];
        snprintf(freq_str, sizeof(freq_str), "%.3f", freq_mhz);
        TJC_SendText("t_freq", freq_str);

        // 更新幅度显示
        float amp_v = current_params.target_vpp_mv / 1000.0;
        TJC_SendFloat("n_amp", amp_v, 2);

        // 更新输出状态
        TJC_SendValue("sw_output", current_params.output_enabled ? 1 : 0);

        // 更新状态信息
        TJC_SendText("t_status", "READY");
    }
}

/**
 * @brief  陶晶池串口屏初始化显示
 * @param  None
 * @retval None
 */
void TJC_InitDisplay(void)
{
    // 发送初始化信息
    TJC_SendText("t_title", "AD9854 DDS");
    TJC_SendText("t_freq", "5.000");
    TJC_SendText("t_freq_unit", "MHz");
    TJC_SendFloat("n_amp", 1.0, 2);
    TJC_SendText("t_amp_unit", "V");
    TJC_SendValue("sw_output", 1);
    TJC_SendText("t_status", "INIT_OK");

    // 设置默认参数
    TJC_SetFrequency(5.0);    // 默认5MHz
    TJC_SetAmplitude(1.0);    // 默认1V
    TJC_SetOutput(1);         // 默认开启输出
}

/**
 * @brief  陶晶池串口屏发送页面跳转命令
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_GotoPage(uint8_t page_id)
{
    USART_Printf("page %d\xff\xff\xff", page_id);
}

/**
 * @brief  陶晶池串口屏发送系统命令
 * @param  cmd: 系统命令
 * @retval None
 */
void TJC_SendSystemCommand(const char* cmd)
{
    if (cmd == NULL) {
        return;
    }

    USART_Printf("%s\xff\xff\xff", cmd);
}

/**
 * @brief  陶晶池串口屏获取控件属性
 * @param  obj_name: 控件名称
 * @param  attr: 属性名称 (如 "val", "txt")
 * @retval None
 */
void TJC_GetAttribute(const char* obj_name, const char* attr)
{
    if (obj_name == NULL || attr == NULL) {
        return;
    }

    USART_Printf("get %s.%s\xff\xff\xff", obj_name, attr);
}

/**
 * @brief  陶晶池串口屏设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: 1-可见, 0-隐藏
 * @retval None
 */
void TJC_SetVisible(const char* obj_name, uint8_t visible)
{
    if (obj_name == NULL) {
        return;
    }

    USART_Printf("vis %s,%d\xff\xff\xff", obj_name, visible);
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
