<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Sun Aug 03 00:24:13 2025
<BR><P>
<H3>Maximum Stack Usage =        324 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
_printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[bf]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1f]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[7]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[21]">CAN1_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[22]">CAN1_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[23]">CAN1_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[20]">CAN1_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4d]">CAN2_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4e]">CAN2_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4f]">CAN2_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4c]">CAN2_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5c]">CRYP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5b]">DCMI_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[18]">DMA1_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3c]">DMA1_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[45]">DMA2_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream2_IRQHandler</a> from usart.o(i.DMA2_Stream2_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[48]">DMA2_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[51]">DMA2_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[52]">DMA2_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream7_IRQHandler</a> from usart.o(i.DMA2_Stream7_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[a]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4a]">ETH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4b]">ETH_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from stm32f4xx_it.o(i.EXTI0_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[35]">EXTI15_10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[24]">EXTI9_5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[11]">FLASH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5e]">FPU_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3d]">FSMC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5d]">HASH_RNG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2f]">I2C2_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2e]">I2C2_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[56]">I2C3_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[55]">I2C3_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[50]">OTG_FS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[37]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[58]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[57]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5a]">OTG_HS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[59]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[e]">PVD_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[b]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[12]">RCC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[36]">RTC_Alarm_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[10]">RTC_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[31]">SPI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[40]">SPI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[9]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[c]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5f]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[f]">TAMP_STAMP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[25]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[28]">TIM1_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[27]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[26]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[29]">TIM2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2a]">TIM3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2b]">TIM4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3f]">TIM5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[43]">TIM6_DAC_IRQHandler</a> from stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[44]">TIM7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[38]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3b]">TIM8_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3a]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[39]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[42]">UART5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[34]">USART3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[54]">USART6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[8]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[d]">WWDG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[64]">__main</a> from __main.o(!!!main) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[63]">_get_lc_ctype</a> from lc_ctype_c.o(locale$$code) referenced from rt_ctype_table.o(.text)
 <LI><a href="#[62]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[61]">_snputc</a> from _snputc.o(.text) referenced 2 times from vsnprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[64]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[65]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[67]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[14a]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[14b]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[68]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[14c]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[69]"></a>_printf_n</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_n.o(.ARM.Collect$$_printf_percent$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_charcount
</UL>

<P><STRONG><a name="[a3]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[6b]"></a>_printf_p</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_p.o(.ARM.Collect$$_printf_percent$$00000002))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_p &rArr; _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
</UL>

<P><STRONG><a name="[6d]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[6f]"></a>_printf_e</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_e.o(.ARM.Collect$$_printf_percent$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_e &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[70]"></a>_printf_g</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_g.o(.ARM.Collect$$_printf_percent$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 324 + Unknown Stack Size
<LI>Call Chain = _printf_g &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[71]"></a>_printf_a</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_a.o(.ARM.Collect$$_printf_percent$$00000006))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = _printf_a &rArr; _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[14d]"></a>_printf_ll</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007))

<P><STRONG><a name="[73]"></a>_printf_i</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_i.o(.ARM.Collect$$_printf_percent$$00000008))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_i &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[75]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[76]"></a>_printf_u</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_u &rArr; _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[77]"></a>_printf_o</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_o &rArr; _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[79]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[7b]"></a>_printf_lli</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lli &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[7d]"></a>_printf_lld</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_lld &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[7e]"></a>_printf_llu</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = _printf_llu &rArr; _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
</UL>

<P><STRONG><a name="[7f]"></a>_printf_llo</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_llo &rArr; _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
</UL>

<P><STRONG><a name="[81]"></a>_printf_llx</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_llx &rArr; _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
</UL>

<P><STRONG><a name="[14e]"></a>_printf_l</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_l.o(.ARM.Collect$$_printf_percent$$00000012))

<P><STRONG><a name="[83]"></a>_printf_c</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_c.o(.ARM.Collect$$_printf_percent$$00000013))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_c &rArr; _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[85]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[87]"></a>_printf_lc</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_lc &rArr; _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[89]"></a>_printf_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 88 + Unknown Stack Size
<LI>Call Chain = _printf_ls &rArr; _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
</UL>

<P><STRONG><a name="[14f]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[95]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[8b]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[150]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[8d]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[151]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[152]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[153]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[154]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[8f]"></a>__rt_lib_init_lc_ctype_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000012))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_ctype_2 &rArr; _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[155]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[156]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[90]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[157]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[158]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[159]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[15a]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[15b]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[15c]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[15d]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[15e]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[15f]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[160]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[161]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[162]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[163]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[9a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[164]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[165]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[166]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[167]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[168]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[169]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[16a]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[66]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[16b]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[92]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[94]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[16c]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[96]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; TJC_Screen_Init &rArr; TJC_InitDisplay &rArr; TJC_SetFrequency &rArr; AD9854_SetTargetFrequency &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[16d]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[c0]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[99]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[16e]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[9b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[bf]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[9d]"></a>vsnprintf</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, vsnprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128 + Unknown Stack Size
<LI>Call Chain = vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Printf
</UL>

<P><STRONG><a name="[a0]"></a>__2snprintf</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, __2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
</UL>

<P><STRONG><a name="[a1]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[135]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendString
</UL>

<P><STRONG><a name="[df]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GetControlParams
</UL>

<P><STRONG><a name="[16f]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[170]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[171]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[11c]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ResetStats
</UL>

<P><STRONG><a name="[172]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[173]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[174]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[175]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[176]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[177]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[a6]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[a8]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[a9]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>

<P><STRONG><a name="[aa]"></a>_printf_truncate_signed</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[ab]"></a>_printf_truncate_unsigned</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_truncate.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[a7]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[74]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_signed
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_u
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_i
</UL>

<P><STRONG><a name="[6a]"></a>_printf_charcount</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, _printf_charcount.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_n
</UL>

<P><STRONG><a name="[178]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[b3]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[9e]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[9f]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>

<P><STRONG><a name="[61]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 2]<UL><LI> __2snprintf.o(.text)
<LI> vsnprintf.o(.text)
</UL>
<P><STRONG><a name="[b6]"></a>_printf_wctomb</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, _printf_wctomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>

<P><STRONG><a name="[7c]"></a>_printf_longlong_dec</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, _printf_longlong_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _printf_longlong_dec &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llu
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lld
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lli
</UL>

<P><STRONG><a name="[b8]"></a>_printf_longlong_oct</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_oct
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_oct
</UL>

<P><STRONG><a name="[78]"></a>_printf_int_oct</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_o
</UL>

<P><STRONG><a name="[80]"></a>_printf_ll_oct</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_oct_int_ll.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_ll_oct &rArr; _printf_longlong_oct &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llo
</UL>

<P><STRONG><a name="[b9]"></a>_printf_longlong_hex</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_hex_ptr
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ll_hex
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[7a]"></a>_printf_int_hex</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = _printf_int_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_truncate_unsigned
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[82]"></a>_printf_ll_hex</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_ll_hex &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_llx
</UL>

<P><STRONG><a name="[6c]"></a>_printf_hex_ptr</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, _printf_hex_int_ll_ptr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_hex_ptr &rArr; _printf_longlong_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_p
</UL>

<P><STRONG><a name="[8e]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[a5]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[179]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[17a]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[b2]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ac]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_hex
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_oct
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_longlong_dec
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[ba]"></a>_printf_fp_hex_real</STRONG> (Thumb, 756 bytes, Stack size 72 bytes, _printf_fp_hex.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex
</UL>

<P><STRONG><a name="[b5]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[bb]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[84]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_char &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_c
</UL>

<P><STRONG><a name="[86]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[bc]"></a>_printf_lcs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wstring
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wchar
</UL>

<P><STRONG><a name="[88]"></a>_printf_wchar</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wchar &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lc
</UL>

<P><STRONG><a name="[8a]"></a>_printf_wstring</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_wchar.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = _printf_wstring &rArr; _printf_lcs_common &rArr; _printf_wctomb &rArr; _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_lcs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_ls
</UL>

<P><STRONG><a name="[ae]"></a>_btod_etento</STRONG> (Thumb, 224 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b7]"></a>_wcrtomb</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, _wcrtomb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _wcrtomb &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_wctomb
</UL>

<P><STRONG><a name="[17b]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[17c]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[93]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[bd]"></a>__rt_ctype_table</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rt_ctype_table.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_wcrtomb
</UL>

<P><STRONG><a name="[98]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[138]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[9c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[17d]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[17e]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[17f]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[af]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[c2]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[c1]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[c3]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[c4]"></a>_e2e</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[b0]"></a>_btod_ediv</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[b1]"></a>_btod_emul</STRONG> (Thumb, 42 bytes, Stack size 28 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[c5]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[ca]"></a>AD9854_CalculateModelCircuitGain</STRONG> (Thumb, 204 bytes, Stack size 88 bytes, ad9854.o(i.AD9854_CalculateModelCircuitGain))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableModelCircuit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutputWithModel
</UL>

<P><STRONG><a name="[d0]"></a>AD9854_CalculateRequiredOutput</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, ad9854.o(i.AD9854_CalculateRequiredOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AD9854_CalculateRequiredOutput &rArr; __aeabi_cdcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableModelCircuit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutputWithModel
</UL>

<P><STRONG><a name="[d3]"></a>AD9854_CalculateRequiredOutputWithModel</STRONG> (Thumb, 68 bytes, Stack size 32 bytes, ad9854.o(i.AD9854_CalculateRequiredOutputWithModel))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutput
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableModelCircuit
</UL>

<P><STRONG><a name="[e1]"></a>AD9854_Delay_us</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, ad9854.o(i.AD9854_Delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteReg
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Reset
</UL>

<P><STRONG><a name="[d4]"></a>AD9854_EnableModelCircuit</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_EnableModelCircuit))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = AD9854_EnableModelCircuit &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_VppToAmplitudeCode
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutputWithModel
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutput
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d6]"></a>AD9854_EnableOutput</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_EnableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = AD9854_EnableOutput &rArr; AD9854_ApplyCurrentParams &rArr; AD9854_SetFrequency &rArr; AD9854_FrequencyToWord &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetAmplitude
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetOutput
</UL>

<P><STRONG><a name="[d9]"></a>AD9854_GPIO_Init</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, ad9854.o(i.AD9854_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AD9854_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
</UL>

<P><STRONG><a name="[de]"></a>AD9854_GetControlParams</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_GetControlParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AD9854_GetControlParams &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e0]"></a>AD9854_Init</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AD9854_Init &rArr; AD9854_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteReg
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Reset
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GPIO_Init
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_System_Init
</UL>

<P><STRONG><a name="[137]"></a>AD9854_InitControlInterface</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, ad9854.o(i.AD9854_InitControlInterface))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e2]"></a>AD9854_Reset</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9854_Reset &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
</UL>

<P><STRONG><a name="[e6]"></a>AD9854_SetTargetAmplitude</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, ad9854.o(i.AD9854_SetTargetAmplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = AD9854_SetTargetAmplitude &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_VppToAmplitudeCode
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutputWithModel
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutput
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
</UL>

<P><STRONG><a name="[e7]"></a>AD9854_SetTargetFrequency</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, ad9854.o(i.AD9854_SetTargetFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = AD9854_SetTargetFrequency &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_VppToAmplitudeCode
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutputWithModel
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
</UL>

<P><STRONG><a name="[e8]"></a>AD9854_System_Init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, main.o(i.AD9854_System_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AD9854_System_Init &rArr; AD9854_Init &rArr; AD9854_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e4]"></a>AD9854_Update</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AD9854_Update &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetFrequency
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetAmplitude
</UL>

<P><STRONG><a name="[c7]"></a>AD9854_ValidateParams</STRONG> (Thumb, 218 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_ValidateParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AD9854_ValidateParams &rArr; __aeabi_cdcmple
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>

<P><STRONG><a name="[d5]"></a>AD9854_VppToAmplitudeCode</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, ad9854.o(i.AD9854_VppToAmplitudeCode))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AD9854_VppToAmplitudeCode &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableModelCircuit
</UL>

<P><STRONG><a name="[e3]"></a>AD9854_WriteReg</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, ad9854.o(i.AD9854_WriteReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AD9854_WriteReg &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteFrequencyWord
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetAmplitude
</UL>

<P><STRONG><a name="[ec]"></a>BSP_Init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp.o(i.BSP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = BSP_Init &rArr; RCC_APB2PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usart.o(i.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; DMA_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetCurrDataCounter
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetITStatus
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, usart.o(i.DMA2_Stream7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DMA2_Stream7_IRQHandler &rArr; DMA_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetITStatus
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f0]"></a>DMA_ClearITPendingBit</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, stm32f4xx_dma.o(i.DMA_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream7_IRQHandler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
</UL>

<P><STRONG><a name="[f2]"></a>DMA_Cmd</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_dma.o(i.DMA_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
</UL>

<P><STRONG><a name="[f4]"></a>DMA_DeInit</STRONG> (Thumb, 462 bytes, Stack size 8 bytes, stm32f4xx_dma.o(i.DMA_DeInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMA_Config
</UL>

<P><STRONG><a name="[f5]"></a>DMA_GetCurrDataCounter</STRONG> (Thumb, 146 bytes, Stack size 8 bytes, stm32f4xx_dma.o(i.DMA_GetCurrDataCounter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_GetCurrDataCounter
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[ef]"></a>DMA_GetITStatus</STRONG> (Thumb, 498 bytes, Stack size 32 bytes, stm32f4xx_dma.o(i.DMA_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream7_IRQHandler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
</UL>

<P><STRONG><a name="[f6]"></a>DMA_ITConfig</STRONG> (Thumb, 238 bytes, Stack size 16 bytes, stm32f4xx_dma.o(i.DMA_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMA_Config
</UL>

<P><STRONG><a name="[f7]"></a>DMA_Init</STRONG> (Thumb, 658 bytes, Stack size 16 bytes, stm32f4xx_dma.o(i.DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMA_Config
</UL>

<P><STRONG><a name="[f1]"></a>DMA_SetCurrDataCounter</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_dma.o(i.DMA_SetCurrDataCounter))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_SetCurrDataCounter
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
</UL>

<P><STRONG><a name="[10e]"></a>DWT_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, systick.o(i.DWT_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DWT_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[a]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[ea]"></a>Delay_ms</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, systick.o(i.Delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_UpdateStats
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_System_Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>

<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler_Internal
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f9]"></a>EXTI0_IRQHandler_Internal</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.EXTI0_IRQHandler_Internal))
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[fb]"></a>ExternalControl_Process</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, external_control.o(i.ExternalControl_Process))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ExternalControl_Process &rArr; TJC_Screen_ProcessData &rArr; USART_ReceiveByte &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_ProcessData
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fd]"></a>FLASH_SetLatency</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_flash.o(i.FLASH_SetLatency))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_SetLatency
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[db]"></a>GPIO_Init</STRONG> (Thumb, 352 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GPIO_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GPIO_Config
</UL>

<P><STRONG><a name="[fe]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 352 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(i.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GPIO_Config
</UL>

<P><STRONG><a name="[ff]"></a>GPIO_ReadOutputDataBit</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_ReadOutputDataBit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[dc]"></a>GPIO_ResetBits</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteReg
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Reset
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GPIO_Init
</UL>

<P><STRONG><a name="[dd]"></a>GPIO_SetBits</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(i.GPIO_SetBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Init
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteReg
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Update
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Reset
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GPIO_Init
</UL>

<P><STRONG><a name="[fa]"></a>GPIO_ToggleBits</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(i.GPIO_ToggleBits))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_ToggleBits
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>

<P><STRONG><a name="[5]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[100]"></a>NVIC_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_NVIC_Config
</UL>

<P><STRONG><a name="[b]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[da]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GPIO_Init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GPIO_Config
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMA_Config
</UL>

<P><STRONG><a name="[ee]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
</UL>

<P><STRONG><a name="[ed]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[111]"></a>RCC_DeInit</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_DeInit))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[12e]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[101]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(i.RCC_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[112]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[102]"></a>RCC_HCLKConfig</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_HCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_HCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[103]"></a>RCC_HSEConfig</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(i.RCC_HSEConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_HSEConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[104]"></a>RCC_PCLK1Config</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_PCLK1Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_PCLK1Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[105]"></a>RCC_PCLK2Config</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_PCLK2Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_PCLK2Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[106]"></a>RCC_PLLCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(i.RCC_PLLCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCC_PLLCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[107]"></a>RCC_PLLConfig</STRONG> (Thumb, 154 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(i.RCC_PLLConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCC_PLLConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[108]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_SYSCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_SYSCLKConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[109]"></a>RCC_WaitForHSEStartUp</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = RCC_WaitForHSEStartUp &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[9]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[132]"></a>SysTick_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, systick.o(i.SysTick_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveByte
</UL>

<P><STRONG><a name="[c]"></a>SysTick_Handler</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler_Internal
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimingDelay_Decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10b]"></a>SysTick_Handler_Internal</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, systick.o(i.SysTick_Handler_Internal))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[10c]"></a>SysTick_Init</STRONG> (Thumb, 114 bytes, Stack size 8 bytes, systick.o(i.SysTick_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SysTick_Init &rArr; NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_ResetStats
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DWT_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10f]"></a>SysTick_ResetStats</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, systick.o(i.SysTick_ResetStats))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[110]"></a>SystemClock_Config</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SystemClock_Config &rArr; RCC_WaitForHSEStartUp &rArr; RCC_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetSYSCLKSource
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_DeInit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5f]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(.text)
</UL>
<P><STRONG><a name="[43]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.TIM6_DAC_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[114]"></a>TJC_InitDisplay</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, external_control.o(i.TJC_InitDisplay))
<BR><BR>[Stack]<UL><LI>Max Depth = 272 + Unknown Stack Size
<LI>Call Chain = TJC_InitDisplay &rArr; TJC_SetFrequency &rArr; AD9854_SetTargetFrequency &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetOutput
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendValue
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendText
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendFloat
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_Init
</UL>

<P><STRONG><a name="[11f]"></a>TJC_ParseCommand</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, external_control.o(i.TJC_ParseCommand))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_ProcessData
</UL>

<P><STRONG><a name="[11b]"></a>TJC_Screen_Init</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, external_control.o(i.TJC_Screen_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = TJC_Screen_Init &rArr; TJC_InitDisplay &rArr; TJC_SetFrequency &rArr; AD9854_SetTargetFrequency &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[fc]"></a>TJC_Screen_ProcessData</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, external_control.o(i.TJC_Screen_ProcessData))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TJC_Screen_ProcessData &rArr; USART_ReceiveByte &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_ParseCommand
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveByte
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExternalControl_Process
</UL>

<P><STRONG><a name="[116]"></a>TJC_SendFloat</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, external_control.o(i.TJC_SendFloat))
<BR><BR>[Stack]<UL><LI>Max Depth = 192 + Unknown Stack Size
<LI>Call Chain = TJC_SendFloat &rArr; USART_Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
</UL>

<P><STRONG><a name="[115]"></a>TJC_SendText</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, external_control.o(i.TJC_SendText))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = TJC_SendText &rArr; USART_Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetOutput
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
</UL>

<P><STRONG><a name="[117]"></a>TJC_SendValue</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, external_control.o(i.TJC_SendValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = TJC_SendValue &rArr; USART_Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetOutput
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
</UL>

<P><STRONG><a name="[119]"></a>TJC_SetAmplitude</STRONG> (Thumb, 142 bytes, Stack size 32 bytes, external_control.o(i.TJC_SetAmplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 240 + Unknown Stack Size
<LI>Call Chain = TJC_SetAmplitude &rArr; AD9854_SetTargetAmplitude &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendText
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendFloat
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
</UL>

<P><STRONG><a name="[118]"></a>TJC_SetFrequency</STRONG> (Thumb, 168 bytes, Stack size 56 bytes, external_control.o(i.TJC_SetFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + Unknown Stack Size
<LI>Call Chain = TJC_SetFrequency &rArr; AD9854_SetTargetFrequency &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendText
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
</UL>

<P><STRONG><a name="[11a]"></a>TJC_SetOutput</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, external_control.o(i.TJC_SetOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = TJC_SetOutput &rArr; TJC_SendValue &rArr; USART_Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableOutput
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendValue
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendText
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_InitDisplay
</UL>

<P><STRONG><a name="[10a]"></a>TimingDelay_Decrement</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(i.TimingDelay_Decrement))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, usart.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USART1_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetCurrDataCounter
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetCurrDataCounter
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11d]"></a>USART1_Init</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, usart.o(i.USART1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = USART1_Init &rArr; USART_DMA_Config &rArr; RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMACmd
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ResetStats
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_NVIC_Config
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GPIO_Config
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMA_Config
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RingBuffer_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_Init
</UL>

<P><STRONG><a name="[123]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, stm32f4xx_usart.o(i.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[12b]"></a>USART_Cmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[129]"></a>USART_DMACmd</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_DMACmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_DMACmd
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[12d]"></a>USART_GetFlagStatus</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendByte
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveByte
</UL>

<P><STRONG><a name="[122]"></a>USART_GetITStatus</STRONG> (Thumb, 302 bytes, Stack size 32 bytes, stm32f4xx_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[12a]"></a>USART_ITConfig</STRONG> (Thumb, 284 bytes, Stack size 32 bytes, stm32f4xx_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[128]"></a>USART_Init</STRONG> (Thumb, 482 bytes, Stack size 48 bytes, stm32f4xx_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[12f]"></a>USART_Module_SendData</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, usart.o(i.USART_Module_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = USART_Module_SendData &rArr; USART_SendByte &rArr; USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendString
</UL>

<P><STRONG><a name="[120]"></a>USART_Printf</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, usart.o(i.USART_Printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = USART_Printf &rArr; vsnprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendValue
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendText
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SendFloat
</UL>

<P><STRONG><a name="[11e]"></a>USART_ReceiveByte</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, usart.o(i.USART_ReceiveByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_ReceiveByte &rArr; USART_GetFlagStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_ProcessData
</UL>

<P><STRONG><a name="[133]"></a>USART_ReceiveData</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_usart.o(i.USART_ReceiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ReceiveData
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveByte
</UL>

<P><STRONG><a name="[12c]"></a>USART_ResetStats</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, usart.o(i.USART_ResetStats))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = USART_ResetStats &rArr; __aeabi_memclr4
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[130]"></a>USART_SendByte</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, usart.o(i.USART_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_SendByte &rArr; USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Module_SendData
</UL>

<P><STRONG><a name="[134]"></a>USART_SendData</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendByte
</UL>

<P><STRONG><a name="[131]"></a>USART_SendString</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, usart.o(i.USART_SendString))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = USART_SendString &rArr; USART_Module_SendData &rArr; USART_SendByte &rArr; USART_SendData
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Module_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Printf
</UL>

<P><STRONG><a name="[8]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[b4]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[ce]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
</UL>

<P><STRONG><a name="[a2]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[f3]"></a>assert_failed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.assert_failed))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ToggleBits
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputDataBit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_SetLatency
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetCurrDataCounter
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ITConfig
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetITStatus
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetCurrDataCounter
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearITPendingBit
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMACmd
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
</UL>

<P><STRONG><a name="[97]"></a>main</STRONG> (Thumb, 282 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = main &rArr; TJC_Screen_Init &rArr; TJC_InitDisplay &rArr; TJC_SetFrequency &rArr; AD9854_SetTargetFrequency &rArr; AD9854_CalculateRequiredOutputWithModel &rArr; AD9854_CalculateModelCircuitGain &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_Screen_Init
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ToggleBits
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputDataBit
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ExternalControl_Process
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BSP_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_InitControlInterface
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_GetControlParams
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableOutput
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableModelCircuit
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_System_Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[91]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[63]"></a>_get_lc_ctype</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_ctype_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_ctype_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rt_ctype_table.o(.text)
</UL>
<P><STRONG><a name="[cd]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
</UL>

<P><STRONG><a name="[139]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[140]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[cf]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_VppToAmplitudeCode
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutput
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_FrequencyToWord
</UL>

<P><STRONG><a name="[13d]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[eb]"></a>__aeabi_d2uiz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_VppToAmplitudeCode
</UL>

<P><STRONG><a name="[13e]"></a>_dfixu</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ValidateParams
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutput
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetFrequency
</UL>

<P><STRONG><a name="[13f]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
</UL>

<P><STRONG><a name="[143]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drcmple
</UL>

<P><STRONG><a name="[cb]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_VppToAmplitudeCode
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_FrequencyToWord
</UL>

<P><STRONG><a name="[141]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[13c]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_ufrom_d
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfixu
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>

<P><STRONG><a name="[13b]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[d1]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ValidateParams
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateRequiredOutput
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetFrequency
</UL>

<P><STRONG><a name="[142]"></a>_drcmple</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmple_InfNaN
</UL>

<P><STRONG><a name="[cc]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_CalculateModelCircuitGain
</UL>

<P><STRONG><a name="[144]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[136]"></a>_dsqrt</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, dsqrt_umaal.o(x$fpl$dsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[180]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)

<P><STRONG><a name="[146]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[121]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetFrequency
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TJC_SetAmplitude
</UL>

<P><STRONG><a name="[147]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[148]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[8c]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[181]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[182]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[d8]"></a>__aeabi_d2ulz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfixull.o(x$fpl$llufromd))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_FrequencyToWord
</UL>

<P><STRONG><a name="[149]"></a>_ll_ufrom_d</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dfixull.o(x$fpl$llufromd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[6e]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 324<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_g
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_e
</UL>

<P><STRONG><a name="[72]"></a>_printf_fp_hex</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf2.o(x$fpl$printf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = _printf_fp_hex &rArr; _printf_fp_hex_real &rArr; _printf_fp_infnan &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_hex_real
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_a
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[e9]"></a>Error_Handler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Error_Handler &rArr; GPIO_ToggleBits
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ToggleBits
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_System_Init
</UL>

<P><STRONG><a name="[113]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[10d]"></a>NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[f8]"></a>SysTick_UpdateStats</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, systick.o(i.SysTick_UpdateStats))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_ms
</UL>

<P><STRONG><a name="[124]"></a>RingBuffer_Init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usart.o(i.RingBuffer_Init))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[126]"></a>USART_DMA_Config</STRONG> (Thumb, 152 bytes, Stack size 64 bytes, usart.o(i.USART_DMA_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = USART_DMA_Config &rArr; RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ITConfig
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[125]"></a>USART_GPIO_Config</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, usart.o(i.USART_GPIO_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = USART_GPIO_Config &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[127]"></a>USART_NVIC_Config</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usart.o(i.USART_NVIC_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_NVIC_Config &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_Init
</UL>

<P><STRONG><a name="[c6]"></a>AD9854_ApplyCurrentParams</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_ApplyCurrentParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = AD9854_ApplyCurrentParams &rArr; AD9854_SetFrequency &rArr; AD9854_FrequencyToWord &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ValidateParams
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetFrequency
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetAmplitude
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetFrequency
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetTargetAmplitude
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableOutput
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableModelCircuit
</UL>

<P><STRONG><a name="[d7]"></a>AD9854_FrequencyToWord</STRONG> (Thumb, 86 bytes, Stack size 48 bytes, ad9854.o(i.AD9854_FrequencyToWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AD9854_FrequencyToWord &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetFrequency
</UL>

<P><STRONG><a name="[c9]"></a>AD9854_SetAmplitude</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, ad9854.o(i.AD9854_SetAmplitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AD9854_SetAmplitude &rArr; AD9854_WriteReg &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteReg
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Update
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_EnableOutput
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>

<P><STRONG><a name="[c8]"></a>AD9854_SetFrequency</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ad9854.o(i.AD9854_SetFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = AD9854_SetFrequency &rArr; AD9854_FrequencyToWord &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_Update
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteFrequencyWord
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_FrequencyToWord
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_ApplyCurrentParams
</UL>

<P><STRONG><a name="[e5]"></a>AD9854_WriteFrequencyWord</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, ad9854.o(i.AD9854_WriteFrequencyWord))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AD9854_WriteFrequencyWord &rArr; AD9854_WriteReg &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_WriteReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9854_SetFrequency
</UL>

<P><STRONG><a name="[145]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[13a]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[ad]"></a>_fp_digits</STRONG> (Thumb, 432 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[62]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
