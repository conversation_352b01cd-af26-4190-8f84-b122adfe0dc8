Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.DMA2_Stream7_IRQHandler) for DMA2_Stream7_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    main.o(i.AD9854_System_Init) refers to ad9854.o(i.AD9854_Init) for AD9854_Init
    main.o(i.AD9854_System_Init) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.AD9854_System_Init) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.AD9854_System_Init) refers to main.o(.data) for ad9854_status
    main.o(i.Error_Handler) refers to stm32f4xx_gpio.o(i.GPIO_ToggleBits) for GPIO_ToggleBits
    main.o(i.Error_Handler) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_DeInit) for RCC_DeInit
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HSEConfig) for RCC_HSEConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) for RCC_WaitForHSEStartUp
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLConfig) for RCC_PLLConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PLLCmd) for RCC_PLLCmd
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    main.o(i.SystemClock_Config) refers to stm32f4xx_flash.o(i.FLASH_SetLatency) for FLASH_SetLatency
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_HCLKConfig) for RCC_HCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK1Config) for RCC_PCLK1Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_PCLK2Config) for RCC_PCLK2Config
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) for RCC_SYSCLKConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource) for RCC_GetSYSCLKSource
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to bsp.o(i.BSP_Init) for BSP_Init
    main.o(i.main) refers to main.o(i.AD9854_System_Init) for AD9854_System_Init
    main.o(i.main) refers to external_control.o(i.TJC_Screen_Init) for TJC_Screen_Init
    main.o(i.main) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.main) refers to ad9854.o(i.AD9854_InitControlInterface) for AD9854_InitControlInterface
    main.o(i.main) refers to ad9854.o(i.AD9854_EnableModelCircuit) for AD9854_EnableModelCircuit
    main.o(i.main) refers to ad9854.o(i.AD9854_SetTargetFrequency) for AD9854_SetTargetFrequency
    main.o(i.main) refers to ad9854.o(i.AD9854_SetTargetAmplitude) for AD9854_SetTargetAmplitude
    main.o(i.main) refers to ad9854.o(i.AD9854_EnableOutput) for AD9854_EnableOutput
    main.o(i.main) refers to systick.o(i.Delay_ms) for Delay_ms
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_ToggleBits) for GPIO_ToggleBits
    main.o(i.main) refers to ad9854.o(i.AD9854_GetControlParams) for AD9854_GetControlParams
    main.o(i.main) refers to external_control.o(i.ExternalControl_Process) for ExternalControl_Process
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.main) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.main) refers to main.o(.data) for ad9854_status
    main.o(i.main) refers to main.o(.bss) for control_params
    stm32f4xx_it.o(i.EXTI0_IRQHandler) refers to main.o(i.EXTI0_IRQHandler_Internal) for EXTI0_IRQHandler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    stm32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.SysTick_Handler_Internal) for SysTick_Handler_Internal
    stm32f4xx_it.o(i.SysTick_Handler) refers to main.o(.data) for uwTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp.o(i.BSP_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    misc.o(i.NVIC_Init) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_PriorityGroupConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SetVectorTable) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.NVIC_SystemLPConfig) refers to main.o(i.assert_failed) for assert_failed
    misc.o(i.SysTick_CLKSourceConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_gpio.o(i.GPIO_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinAFConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_PinLockConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ResetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_SetBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_ToggleBits) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_Write) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_gpio.o(i.GPIO_WriteBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_BackupResetCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_HSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_I2SCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSEModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LSICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_MCO2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PCLK2Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLI2SConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAICmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_PLLSAIConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_RTCCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_SYSCLKConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_flash.o(i.FLASH_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_DataCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BORConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_BootConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_SetLatency) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_usart.o(i.USART_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ClockInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DMACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_HalfDuplexCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_usart.o(i.USART_IrDACmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_IrDAConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_LINCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OneBitMethodCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_OverSampling8Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiveData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendBreak) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SendData) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetAddress) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetGuardTime) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SetPrescaler) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_SmartCardNACKCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_usart.o(i.USART_WakeUpConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearFlag) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ClearITPendingBit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Cmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DeInit) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_FlowControllerConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCmdStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFIFOStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetFlagStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_GetITStatus) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_ITConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_Init) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_MemoryTargetConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig) refers to main.o(i.assert_failed) for assert_failed
    stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) refers to main.o(i.assert_failed) for assert_failed
    systick.o(i.DWT_Init) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.Delay_ms) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_ms) refers to systick.o(.data) for s_delay_counter
    systick.o(i.Delay_s) refers to systick.o(i.Delay_ms) for Delay_ms
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_GetCalibratedDelay) for SysTick_GetCalibratedDelay
    systick.o(i.Delay_us) refers to systick.o(i.SysTick_UpdateStats) for SysTick_UpdateStats
    systick.o(i.Delay_us) refers to systick.o(.data) for s_dwt_initialized
    systick.o(i.SysTick_Calibrate) refers to systick.o(i.Delay_us) for Delay_us
    systick.o(i.SysTick_Calibrate) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetCalibratedDelay) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_GetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_GetTick) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_GetTimestamp_us) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_GetUptime_ms) refers to systick.o(.data) for g_system_uptime_ms
    systick.o(i.SysTick_Handler_Internal) refers to systick.o(.data) for g_systick_counter
    systick.o(i.SysTick_Init) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.SysTick_Init) refers to systick.o(i.DWT_Init) for DWT_Init
    systick.o(i.SysTick_Init) refers to systick.o(i.SysTick_ResetStats) for SysTick_ResetStats
    systick.o(i.SysTick_Init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    systick.o(i.SysTick_Init) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_ResetStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_SetTemperatureCompensation) refers to systick.o(.data) for g_systick_cal
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.bss) for g_systick_stats
    systick.o(i.SysTick_UpdateStats) refers to systick.o(.data) for g_system_uptime_ms
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    usart.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.data) for g_usart_rx_complete_flag
    usart.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    usart.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    usart.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetCurrDataCounter) for DMA_GetCurrDataCounter
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for g_usart_rx_complete_flag
    usart.o(i.USART1_Init) refers to usart.o(i.RingBuffer_Init) for RingBuffer_Init
    usart.o(i.USART1_Init) refers to usart.o(i.USART_GPIO_Config) for USART_GPIO_Config
    usart.o(i.USART1_Init) refers to usart.o(i.USART_DMA_Config) for USART_DMA_Config
    usart.o(i.USART1_Init) refers to usart.o(i.USART_NVIC_Config) for USART_NVIC_Config
    usart.o(i.USART1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_DMACmd) for USART_DMACmd
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.USART1_Init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.USART1_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.USART1_Init) refers to usart.o(i.USART_ResetStats) for USART_ResetStats
    usart.o(i.USART1_Init) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    usart.o(i.USART_DMA_Config) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    usart.o(i.USART_DMA_Config) refers to usart.o(.bss) for s_tx_buffer
    usart.o(i.USART_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.USART_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.USART_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.USART_GetStats) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    usart.o(i.USART_GetStats) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_IsTxComplete) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.USART_Module_ReceiveData) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    usart.o(i.USART_Module_ReceiveData) refers to usart.o(i.USART_ReceiveByte) for USART_ReceiveByte
    usart.o(i.USART_Module_SendData) refers to usart.o(i.USART_SendByte) for USART_SendByte
    usart.o(i.USART_NVIC_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.USART_Printf) refers to vsnprintf.o(.text) for vsnprintf
    usart.o(i.USART_Printf) refers to usart.o(i.USART_SendString) for USART_SendString
    usart.o(i.USART_Printf) refers to usart.o(.bss) for s_printf_buffer
    usart.o(i.USART_ReceiveByte) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    usart.o(i.USART_ReceiveByte) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_ReceiveByte) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART_ReceiveByte) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_ResetStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.USART_ResetStats) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_SendByte) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(i.USART_SendByte) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    usart.o(i.USART_SendByte) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_SendData_DMA) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    usart.o(i.USART_SendData_DMA) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    usart.o(i.USART_SendData_DMA) refers to stm32f4xx_dma.o(i.DMA_SetCurrDataCounter) for DMA_SetCurrDataCounter
    usart.o(i.USART_SendData_DMA) refers to usart.o(.bss) for g_usart1_handle
    usart.o(i.USART_SendData_DMA) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.USART_SendHex) refers to usart.o(i.USART_Printf) for USART_Printf
    usart.o(i.USART_SendString) refers to strlen.o(.text) for strlen
    usart.o(i.USART_SendString) refers to usart.o(i.USART_Module_SendData) for USART_Module_SendData
    usart.o(i.USART_WaitTxComplete) refers to systick.o(i.SysTick_GetTick) for SysTick_GetTick
    usart.o(i.USART_WaitTxComplete) refers to usart.o(.data) for g_usart_tx_complete_flag
    usart.o(i.fgetc) refers to usart.o(i.USART_ReceiveByte) for USART_ReceiveByte
    usart.o(i.fputc) refers to usart.o(i.USART_SendByte) for USART_SendByte
    ad9854.o(i.AD9854_AmplitudeCodeToVpp) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9854.o(i.AD9854_AmplitudeCodeToVpp) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_AmplitudeCodeToVpp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(i.AD9854_ValidateParams) for AD9854_ValidateParams
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(i.AD9854_SetFrequency) for AD9854_SetFrequency
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(i.AD9854_SetAmplitude) for AD9854_SetAmplitude
    ad9854.o(i.AD9854_ApplyCurrentParams) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    ad9854.o(i.AD9854_CalculateModelCircuitGain) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_CalculateRequiredOutput) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_CalculateRequiredOutput) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_CalculateRequiredOutput) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) refers to ad9854.o(i.AD9854_CalculateModelCircuitGain) for AD9854_CalculateModelCircuitGain
    ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_CalculateModelCircuitGain) for AD9854_CalculateModelCircuitGain
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) for AD9854_CalculateRequiredOutputWithModel
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_EnableModelCircuit) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_EnableOutput) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_EnableOutput) refers to ad9854.o(i.AD9854_SetAmplitude) for AD9854_SetAmplitude
    ad9854.o(i.AD9854_EnableOutput) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_FrequencyToWord) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_FrequencyToWord) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_FrequencyToWord) refers to dfixull.o(x$fpl$llufromd) for __aeabi_d2ulz
    ad9854.o(i.AD9854_FrequencyToWord) refers to ad9854.o(.data) for freq_word
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_GetControlParams) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ad9854.o(i.AD9854_GetControlParams) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_GPIO_Init) for AD9854_GPIO_Init
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_Reset) for AD9854_Reset
    ad9854.o(i.AD9854_Init) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_WriteReg) for AD9854_WriteReg
    ad9854.o(i.AD9854_Init) refers to ad9854.o(i.AD9854_Update) for AD9854_Update
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_SetTargetFrequency) for AD9854_SetTargetFrequency
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_SetTargetAmplitude) for AD9854_SetTargetAmplitude
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_SetGainFactor) for AD9854_SetGainFactor
    ad9854.o(i.AD9854_ProcessCommand) refers to ad9854.o(i.AD9854_EnableOutput) for AD9854_EnableOutput
    ad9854.o(i.AD9854_Reset) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_Reset) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    ad9854.o(i.AD9854_Reset) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_SetAmplitude) refers to ad9854.o(i.AD9854_WriteReg) for AD9854_WriteReg
    ad9854.o(i.AD9854_SetAmplitude) refers to ad9854.o(i.AD9854_Update) for AD9854_Update
    ad9854.o(i.AD9854_SetFrequency) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetFrequency) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetFrequency) refers to ad9854.o(i.AD9854_FrequencyToWord) for AD9854_FrequencyToWord
    ad9854.o(i.AD9854_SetFrequency) refers to ad9854.o(i.AD9854_WriteFrequencyWord) for AD9854_WriteFrequencyWord
    ad9854.o(i.AD9854_SetFrequency) refers to ad9854.o(i.AD9854_Update) for AD9854_Update
    ad9854.o(i.AD9854_SetGainFactor) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetGainFactor) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_SetGainFactor) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) for AD9854_CalculateRequiredOutputWithModel
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_CalculateRequiredOutput) for AD9854_CalculateRequiredOutput
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_SetTargetAmplitude) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_SetTargetFrequency) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_SetTargetFrequency) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_CalculateModelCircuitGain) for AD9854_CalculateModelCircuitGain
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_CalculateRequiredOutputWithModel) for AD9854_CalculateRequiredOutputWithModel
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_VppToAmplitudeCode) for AD9854_VppToAmplitudeCode
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(i.AD9854_ApplyCurrentParams) for AD9854_ApplyCurrentParams
    ad9854.o(i.AD9854_SetTargetFrequency) refers to ad9854.o(.data) for g_control_params
    ad9854.o(i.AD9854_Update) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_Update) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    ad9854.o(i.AD9854_Update) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_ValidateParams) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    ad9854.o(i.AD9854_ValidateParams) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    ad9854.o(i.AD9854_VppToAmplitudeCode) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    ad9854.o(i.AD9854_VppToAmplitudeCode) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9854.o(i.AD9854_VppToAmplitudeCode) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9854.o(i.AD9854_WriteFrequencyWord) refers to ad9854.o(i.AD9854_WriteReg) for AD9854_WriteReg
    ad9854.o(i.AD9854_WriteFrequencyWord) refers to ad9854.o(.data) for freq_word
    ad9854.o(i.AD9854_WriteReg) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ad9854.o(i.AD9854_WriteReg) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ad9854.o(i.AD9854_WriteReg) refers to ad9854.o(i.AD9854_Delay_us) for AD9854_Delay_us
    external_control.o(i.ExternalControl_Process) refers to external_control.o(i.TJC_Screen_ProcessData) for TJC_Screen_ProcessData
    external_control.o(i.TJC_GetAttribute) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_GotoPage) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_InitDisplay) refers to external_control.o(i.TJC_SendText) for TJC_SendText
    external_control.o(i.TJC_InitDisplay) refers to external_control.o(i.TJC_SendFloat) for TJC_SendFloat
    external_control.o(i.TJC_InitDisplay) refers to external_control.o(i.TJC_SendValue) for TJC_SendValue
    external_control.o(i.TJC_InitDisplay) refers to external_control.o(i.TJC_SetFrequency) for TJC_SetFrequency
    external_control.o(i.TJC_InitDisplay) refers to external_control.o(i.TJC_SetAmplitude) for TJC_SetAmplitude
    external_control.o(i.TJC_InitDisplay) refers to external_control.o(i.TJC_SetOutput) for TJC_SetOutput
    external_control.o(i.TJC_Screen_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    external_control.o(i.TJC_Screen_Init) refers to usart.o(i.USART1_Init) for USART1_Init
    external_control.o(i.TJC_Screen_Init) refers to systick.o(i.Delay_ms) for Delay_ms
    external_control.o(i.TJC_Screen_Init) refers to external_control.o(i.TJC_InitDisplay) for TJC_InitDisplay
    external_control.o(i.TJC_Screen_Init) refers to external_control.o(.bss) for tjc_screen_ctrl
    external_control.o(i.TJC_Screen_ProcessData) refers to usart.o(i.USART_ReceiveByte) for USART_ReceiveByte
    external_control.o(i.TJC_Screen_ProcessData) refers to external_control.o(i.TJC_ParseCommand) for TJC_ParseCommand
    external_control.o(i.TJC_Screen_ProcessData) refers to external_control.o(.bss) for tjc_screen_ctrl
    external_control.o(i.TJC_SendFloat) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_SendSystemCommand) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_SendText) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_SendValue) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_SetAmplitude) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    external_control.o(i.TJC_SetAmplitude) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    external_control.o(i.TJC_SetAmplitude) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    external_control.o(i.TJC_SetAmplitude) refers to external_control.o(i.TJC_SendText) for TJC_SendText
    external_control.o(i.TJC_SetAmplitude) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    external_control.o(i.TJC_SetAmplitude) refers to ad9854.o(i.AD9854_SetTargetAmplitude) for AD9854_SetTargetAmplitude
    external_control.o(i.TJC_SetAmplitude) refers to external_control.o(i.TJC_SendFloat) for TJC_SendFloat
    external_control.o(i.TJC_SetFrequency) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    external_control.o(i.TJC_SetFrequency) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    external_control.o(i.TJC_SetFrequency) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    external_control.o(i.TJC_SetFrequency) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    external_control.o(i.TJC_SetFrequency) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    external_control.o(i.TJC_SetFrequency) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    external_control.o(i.TJC_SetFrequency) refers to external_control.o(i.TJC_SendText) for TJC_SendText
    external_control.o(i.TJC_SetFrequency) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    external_control.o(i.TJC_SetFrequency) refers to ad9854.o(i.AD9854_SetTargetFrequency) for AD9854_SetTargetFrequency
    external_control.o(i.TJC_SetFrequency) refers to __2snprintf.o(.text) for __2snprintf
    external_control.o(i.TJC_SetOutput) refers to ad9854.o(i.AD9854_EnableOutput) for AD9854_EnableOutput
    external_control.o(i.TJC_SetOutput) refers to external_control.o(i.TJC_SendValue) for TJC_SendValue
    external_control.o(i.TJC_SetOutput) refers to external_control.o(i.TJC_SendText) for TJC_SendText
    external_control.o(i.TJC_SetVisible) refers to usart.o(i.USART_Printf) for USART_Printf
    external_control.o(i.TJC_UpdateAllDisplay) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    external_control.o(i.TJC_UpdateAllDisplay) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    external_control.o(i.TJC_UpdateAllDisplay) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    external_control.o(i.TJC_UpdateAllDisplay) refers to ad9854.o(i.AD9854_GetControlParams) for AD9854_GetControlParams
    external_control.o(i.TJC_UpdateAllDisplay) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    external_control.o(i.TJC_UpdateAllDisplay) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    external_control.o(i.TJC_UpdateAllDisplay) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    external_control.o(i.TJC_UpdateAllDisplay) refers to __2snprintf.o(.text) for __2snprintf
    external_control.o(i.TJC_UpdateAllDisplay) refers to external_control.o(i.TJC_SendText) for TJC_SendText
    external_control.o(i.TJC_UpdateAllDisplay) refers to external_control.o(i.TJC_SendFloat) for TJC_SendFloat
    external_control.o(i.TJC_UpdateAllDisplay) refers to external_control.o(i.TJC_SendValue) for TJC_SendValue
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixull.o(x$fpl$llufromdr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixull.o(x$fpl$llufromdr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing bsp.o(.rev16_text), (4 bytes).
    Removing bsp.o(.revsh_text), (4 bytes).
    Removing bsp.o(.rrx_text), (6 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (80 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (88 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (92 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (80 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (416 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (204 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (268 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (160 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (280 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd), (104 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (68 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (52 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (96 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (80 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (128 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (88 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (116 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (412 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (72 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (76 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (56 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (68 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (164 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (300 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (104 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (76 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (140 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (116 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (84 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (148 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (160 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (152 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (220 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (212 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (344 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (184 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (188 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (156 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (180 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (104 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (164 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (128 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (184 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (256 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (220 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (192 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (184 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (580 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (208 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (216 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.Delay_s), (24 bytes).
    Removing systick.o(i.Delay_us), (80 bytes).
    Removing systick.o(i.SysTick_Calibrate), (96 bytes).
    Removing systick.o(i.SysTick_GetCalibratedDelay), (72 bytes).
    Removing systick.o(i.SysTick_GetStats), (24 bytes).
    Removing systick.o(i.SysTick_GetTimestamp_us), (56 bytes).
    Removing systick.o(i.SysTick_GetUptime_ms), (12 bytes).
    Removing systick.o(i.SysTick_NonBlocking_Init), (32 bytes).
    Removing systick.o(i.SysTick_NonBlocking_IsCompleted), (48 bytes).
    Removing systick.o(i.SysTick_SetTemperatureCompensation), (24 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.RingBuffer_Read), (60 bytes).
    Removing usart.o(i.RingBuffer_Write), (68 bytes).
    Removing usart.o(i.USART_GetStats), (28 bytes).
    Removing usart.o(i.USART_IsTxComplete), (12 bytes).
    Removing usart.o(i.USART_Module_ReceiveData), (72 bytes).
    Removing usart.o(i.USART_SendData_DMA), (104 bytes).
    Removing usart.o(i.USART_SendHex), (92 bytes).
    Removing usart.o(i.USART_WaitTxComplete), (44 bytes).
    Removing usart.o(i.fgetc), (28 bytes).
    Removing usart.o(i.fputc), (16 bytes).
    Removing ad9854.o(.rev16_text), (4 bytes).
    Removing ad9854.o(.revsh_text), (4 bytes).
    Removing ad9854.o(.rrx_text), (6 bytes).
    Removing ad9854.o(i.AD9854_AmplitudeCodeToVpp), (72 bytes).
    Removing ad9854.o(i.AD9854_ProcessCommand), (132 bytes).
    Removing ad9854.o(i.AD9854_SetGainFactor), (152 bytes).
    Removing external_control.o(.rev16_text), (4 bytes).
    Removing external_control.o(.revsh_text), (4 bytes).
    Removing external_control.o(.rrx_text), (6 bytes).
    Removing external_control.o(i.TJC_GetAttribute), (44 bytes).
    Removing external_control.o(i.TJC_GotoPage), (28 bytes).
    Removing external_control.o(i.TJC_SendSystemCommand), (28 bytes).
    Removing external_control.o(i.TJC_SetVisible), (40 bytes).
    Removing external_control.o(i.TJC_UpdateAllDisplay), (240 bytes).

191 unused section(s) (total 16730 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dfixull.s                       0x00000000   Number         0  dfixull.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    Library\\misc.c                          0x00000000   Number         0  misc.o ABSOLUTE
    Library\\stm32f4xx_dma.c                 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\\stm32f4xx_flash.c               0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\\stm32f4xx_gpio.c                0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\\stm32f4xx_rcc.c                 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\\stm32f4xx_usart.c               0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f4xx_dma.c                  0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    Library\stm32f4xx_flash.c                0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    Library\stm32f4xx_gpio.c                 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    Library\stm32f4xx_rcc.c                  0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    Library\stm32f4xx_usart.c                0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    Modules\Control\external_control.c       0x00000000   Number         0  external_control.o ABSOLUTE
    Modules\Core\systick.c                   0x00000000   Number         0  systick.o ABSOLUTE
    Modules\Core\usart.c                     0x00000000   Number         0  usart.o ABSOLUTE
    Modules\Generation\ad9854.c              0x00000000   Number         0  ad9854.o ABSOLUTE
    Modules\\Control\\external_control.c     0x00000000   Number         0  external_control.o ABSOLUTE
    Modules\\Core\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    Modules\\Core\\usart.c                   0x00000000   Number         0  usart.o ABSOLUTE
    Modules\\Generation\\ad9854.c            0x00000000   Number         0  ad9854.o ABSOLUTE
    Start\startup_stm32f40_41xxx.s           0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    User\\bsp.c                              0x00000000   Number         0  bsp.o ABSOLUTE
    User\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    User\\stm32f4xx_it.c                     0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\\system_stm32f4xx.c                 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    User\bsp.c                               0x00000000   Number         0  bsp.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f4xx_it.c                      0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    User\system_stm32f4xx.c                  0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000314   Section        0  vsnprintf.o(.text)
    .text                                    0x08000348   Section        0  __2snprintf.o(.text)
    .text                                    0x08000380   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000508   Section        0  strlen.o(.text)
    .text                                    0x08000546   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080005aa   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080005f8   Section        0  heapauxi.o(.text)
    .text                                    0x080005fe   Section        0  _rserrno.o(.text)
    .text                                    0x08000614   Section        0  _printf_pad.o(.text)
    .text                                    0x08000662   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000686   Section        0  _printf_str.o(.text)
    .text                                    0x080006d8   Section        0  _printf_dec.o(.text)
    .text                                    0x08000750   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000778   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800077b   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000b98   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000b99   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000bc8   Section        0  _sputc.o(.text)
    .text                                    0x08000bd2   Section        0  _snputc.o(.text)
    .text                                    0x08000be4   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000ca0   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000d1c   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000d1d   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000d8c   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000d8d   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000e20   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000e28   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000e30   Section      138  lludiv10.o(.text)
    .text                                    0x08000eba   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000f6c   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001268   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080012e8   Section        0  _printf_char.o(.text)
    .text                                    0x08001314   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001340   Section        0  bigflt0.o(.text)
    .text                                    0x08001424   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001464   Section        8  libspace.o(.text)
    .text                                    0x0800146c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080014b8   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080014c8   Section        0  exit.o(.text)
    .text                                    0x080014dc   Section      128  strcmpv7m.o(.text)
    .text                                    0x0800155c   Section        0  sys_exit.o(.text)
    .text                                    0x08001568   Section        2  use_no_semi.o(.text)
    .text                                    0x0800156a   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x0800156a   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080015a8   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080015ee   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800164e   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001986   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001a62   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001a8c   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001ab6   Section      580  btod.o(CL$$btod_mult_common)
    i.AD9854_ApplyCurrentParams              0x08001cfc   Section        0  ad9854.o(i.AD9854_ApplyCurrentParams)
    AD9854_ApplyCurrentParams                0x08001cfd   Thumb Code    74  ad9854.o(i.AD9854_ApplyCurrentParams)
    i.AD9854_CalculateModelCircuitGain       0x08001d4c   Section        0  ad9854.o(i.AD9854_CalculateModelCircuitGain)
    i.AD9854_CalculateRequiredOutput         0x08001e40   Section        0  ad9854.o(i.AD9854_CalculateRequiredOutput)
    i.AD9854_CalculateRequiredOutputWithModel 0x08001ec4   Section        0  ad9854.o(i.AD9854_CalculateRequiredOutputWithModel)
    i.AD9854_Delay_us                        0x08001f08   Section        0  ad9854.o(i.AD9854_Delay_us)
    i.AD9854_EnableModelCircuit              0x08001f1c   Section        0  ad9854.o(i.AD9854_EnableModelCircuit)
    i.AD9854_EnableOutput                    0x08001fa8   Section        0  ad9854.o(i.AD9854_EnableOutput)
    i.AD9854_FrequencyToWord                 0x08001fc8   Section        0  ad9854.o(i.AD9854_FrequencyToWord)
    AD9854_FrequencyToWord                   0x08001fc9   Thumb Code    86  ad9854.o(i.AD9854_FrequencyToWord)
    i.AD9854_GPIO_Init                       0x08002034   Section        0  ad9854.o(i.AD9854_GPIO_Init)
    i.AD9854_GetControlParams                0x080020dc   Section        0  ad9854.o(i.AD9854_GetControlParams)
    i.AD9854_Init                            0x080020f8   Section        0  ad9854.o(i.AD9854_Init)
    i.AD9854_InitControlInterface            0x08002154   Section        0  ad9854.o(i.AD9854_InitControlInterface)
    i.AD9854_Reset                           0x08002170   Section        0  ad9854.o(i.AD9854_Reset)
    i.AD9854_SetAmplitude                    0x08002194   Section        0  ad9854.o(i.AD9854_SetAmplitude)
    AD9854_SetAmplitude                      0x08002195   Thumb Code    54  ad9854.o(i.AD9854_SetAmplitude)
    i.AD9854_SetFrequency                    0x080021cc   Section        0  ad9854.o(i.AD9854_SetFrequency)
    AD9854_SetFrequency                      0x080021cd   Thumb Code    84  ad9854.o(i.AD9854_SetFrequency)
    i.AD9854_SetTargetAmplitude              0x08002230   Section        0  ad9854.o(i.AD9854_SetTargetAmplitude)
    i.AD9854_SetTargetFrequency              0x080022e8   Section        0  ad9854.o(i.AD9854_SetTargetFrequency)
    i.AD9854_System_Init                     0x0800238c   Section        0  main.o(i.AD9854_System_Init)
    i.AD9854_Update                          0x080023ac   Section        0  ad9854.o(i.AD9854_Update)
    i.AD9854_ValidateParams                  0x080023d0   Section        0  ad9854.o(i.AD9854_ValidateParams)
    i.AD9854_VppToAmplitudeCode              0x080024dc   Section        0  ad9854.o(i.AD9854_VppToAmplitudeCode)
    i.AD9854_WriteFrequencyWord              0x08002538   Section        0  ad9854.o(i.AD9854_WriteFrequencyWord)
    AD9854_WriteFrequencyWord                0x08002539   Thumb Code    34  ad9854.o(i.AD9854_WriteFrequencyWord)
    i.AD9854_WriteReg                        0x08002560   Section        0  ad9854.o(i.AD9854_WriteReg)
    i.BSP_Init                               0x080025e0   Section        0  bsp.o(i.BSP_Init)
    i.BusFault_Handler                       0x08002614   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream2_IRQHandler                0x08002618   Section        0  usart.o(i.DMA2_Stream2_IRQHandler)
    i.DMA2_Stream7_IRQHandler                0x08002688   Section        0  usart.o(i.DMA2_Stream7_IRQHandler)
    i.DMA_ClearITPendingBit                  0x080026ec   Section        0  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x080027ec   Section        0  stm32f4xx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x080028c4   Section        0  stm32f4xx_dma.o(i.DMA_DeInit)
    i.DMA_GetCurrDataCounter                 0x08002ac0   Section        0  stm32f4xx_dma.o(i.DMA_GetCurrDataCounter)
    i.DMA_GetITStatus                        0x08002b74   Section        0  stm32f4xx_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x08002e20   Section        0  stm32f4xx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08002f30   Section        0  stm32f4xx_dma.o(i.DMA_Init)
    i.DMA_SetCurrDataCounter                 0x080031e8   Section        0  stm32f4xx_dma.o(i.DMA_SetCurrDataCounter)
    i.DWT_Init                               0x0800329c   Section        0  systick.o(i.DWT_Init)
    i.DebugMon_Handler                       0x080032f8   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080032fc   Section        0  systick.o(i.Delay_ms)
    i.EXTI0_IRQHandler                       0x08003328   Section        0  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI0_IRQHandler_Internal              0x08003330   Section        0  main.o(i.EXTI0_IRQHandler_Internal)
    i.Error_Handler                          0x08003334   Section        0  main.o(i.Error_Handler)
    Error_Handler                            0x08003335   Thumb Code    18  main.o(i.Error_Handler)
    i.ExternalControl_Process                0x0800334c   Section        0  external_control.o(i.ExternalControl_Process)
    i.FLASH_SetLatency                       0x08003354   Section        0  stm32f4xx_flash.o(i.FLASH_SetLatency)
    i.GPIO_Init                              0x080033c8   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08003570   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadOutputDataBit                 0x08003718   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit)
    i.GPIO_ResetBits                         0x08003824   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x080038d4   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.GPIO_ToggleBits                        0x08003984   Section        0  stm32f4xx_gpio.o(i.GPIO_ToggleBits)
    i.HardFault_Handler                      0x08003a28   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08003a2c   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08003a30   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08003a34   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_SetPriority                       0x08003af4   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08003af5   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.PendSV_Handler                         0x08003b1c   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x08003b20   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08003b88   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08003bec   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_DeInit                             0x08003c54   Section        0  stm32f4xx_rcc.o(i.RCC_DeInit)
    i.RCC_GetClocksFreq                      0x08003cb8   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.RCC_GetFlagStatus                      0x08003da0   Section        0  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    i.RCC_GetSYSCLKSource                    0x08003e44   Section        0  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    i.RCC_HCLKConfig                         0x08003e54   Section        0  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    i.RCC_HSEConfig                          0x08003eb4   Section        0  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    i.RCC_PCLK1Config                        0x08003ef8   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    i.RCC_PCLK2Config                        0x08003f50   Section        0  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    i.RCC_PLLCmd                             0x08003fa8   Section        0  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    i.RCC_PLLConfig                          0x08003fe0   Section        0  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    i.RCC_SYSCLKConfig                       0x08004098   Section        0  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    i.RCC_WaitForHSEStartUp                  0x080040e0   Section        0  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    i.RingBuffer_Init                        0x08004118   Section        0  usart.o(i.RingBuffer_Init)
    RingBuffer_Init                          0x08004119   Thumb Code    16  usart.o(i.RingBuffer_Init)
    i.SVC_Handler                            0x08004128   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x0800412c   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x0800412d   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_GetTick                        0x08004218   Section        0  systick.o(i.SysTick_GetTick)
    i.SysTick_Handler                        0x08004224   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Handler_Internal               0x08004240   Section        0  systick.o(i.SysTick_Handler_Internal)
    i.SysTick_Init                           0x08004278   Section        0  systick.o(i.SysTick_Init)
    i.SysTick_ResetStats                     0x080042f4   Section        0  systick.o(i.SysTick_ResetStats)
    i.SysTick_UpdateStats                    0x08004314   Section        0  systick.o(i.SysTick_UpdateStats)
    SysTick_UpdateStats                      0x08004315   Thumb Code    80  systick.o(i.SysTick_UpdateStats)
    i.SystemClock_Config                     0x08004374   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080043e0   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM6_DAC_IRQHandler                    0x08004448   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TJC_InitDisplay                        0x0800444c   Section        0  external_control.o(i.TJC_InitDisplay)
    i.TJC_ParseCommand                       0x08004510   Section        0  external_control.o(i.TJC_ParseCommand)
    i.TJC_Screen_Init                        0x08004518   Section        0  external_control.o(i.TJC_Screen_Init)
    i.TJC_Screen_ProcessData                 0x08004558   Section        0  external_control.o(i.TJC_Screen_ProcessData)
    i.TJC_SendFloat                          0x08004584   Section        0  external_control.o(i.TJC_SendFloat)
    i.TJC_SendText                           0x080045e4   Section        0  external_control.o(i.TJC_SendText)
    i.TJC_SendValue                          0x08004610   Section        0  external_control.o(i.TJC_SendValue)
    i.TJC_SetAmplitude                       0x08004638   Section        0  external_control.o(i.TJC_SetAmplitude)
    i.TJC_SetFrequency                       0x08004704   Section        0  external_control.o(i.TJC_SetFrequency)
    i.TJC_SetOutput                          0x080047f4   Section        0  external_control.o(i.TJC_SetOutput)
    i.TimingDelay_Decrement                  0x08004840   Section        0  main.o(i.TimingDelay_Decrement)
    i.USART1_IRQHandler                      0x08004844   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART1_Init                            0x080048e0   Section        0  usart.o(i.USART1_Init)
    i.USART_ClearITPendingBit                0x080049c0   Section        0  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08004ac0   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_DMACmd                           0x08004b7c   Section        0  stm32f4xx_usart.o(i.USART_DMACmd)
    i.USART_DMA_Config                       0x08004c4c   Section        0  usart.o(i.USART_DMA_Config)
    USART_DMA_Config                         0x08004c4d   Thumb Code   152  usart.o(i.USART_DMA_Config)
    i.USART_GPIO_Config                      0x08004cf4   Section        0  usart.o(i.USART_GPIO_Config)
    USART_GPIO_Config                        0x08004cf5   Thumb Code    94  usart.o(i.USART_GPIO_Config)
    i.USART_GetFlagStatus                    0x08004d58   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08004e60   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08004fd4   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08005134   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_Module_SendData                  0x08005360   Section        0  usart.o(i.USART_Module_SendData)
    i.USART_NVIC_Config                      0x08005392   Section        0  usart.o(i.USART_NVIC_Config)
    USART_NVIC_Config                        0x08005393   Thumb Code    92  usart.o(i.USART_NVIC_Config)
    i.USART_Printf                           0x080053f0   Section        0  usart.o(i.USART_Printf)
    i.USART_ReceiveByte                      0x08005424   Section        0  usart.o(i.USART_ReceiveByte)
    i.USART_ReceiveData                      0x08005474   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_ResetStats                       0x0800550c   Section        0  usart.o(i.USART_ResetStats)
    i.USART_SendByte                         0x0800551c   Section        0  usart.o(i.USART_SendByte)
    i.USART_SendData                         0x08005564   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.USART_SendString                       0x08005610   Section        0  usart.o(i.USART_SendString)
    i.UsageFault_Handler                     0x0800562c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08005630   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_sqrt                          0x08005660   Section        0  sqrt.o(i.__hardfp_sqrt)
    i._is_digit                              0x080056da   Section        0  __printf_wp.o(i._is_digit)
    i.assert_failed                          0x080056e8   Section        0  main.o(i.assert_failed)
    i.main                                   0x080056ec   Section        0  main.o(i.main)
    locale$$code                             0x0800582c   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08005858   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dadd                               0x08005884   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08005884   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08005895   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x080059d4   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x080059d4   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x080059ec   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x080059ec   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080059f3   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08005c9c   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08005c9c   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dleqf                              0x08005cf8   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08005cf8   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08005d70   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08005d70   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08005ec4   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08005ec4   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08005f60   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08005f60   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x08005f6c   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x08005f6c   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x08005fd8   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08005fd8   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08005ff0   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08005ff0   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08006188   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08006188   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08006199   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800635c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800635c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x080063b2   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x080063b2   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800643e   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800643e   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$llufromd                           0x08006448   Section      120  dfixull.o(x$fpl$llufromd)
    $v0                                      0x08006448   Number         0  dfixull.o(x$fpl$llufromd)
    x$fpl$printf1                            0x080064c0   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080064c0   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x080064c4   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x080064c4   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x080064c8   Section       17  __printf_flags_ss_wp.o(.constdata)
    x$fpl$usenofp                            0x080064c8   Section        0  usenofp.o(x$fpl$usenofp)
    maptable                                 0x080064c8   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080064dc   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x080064dc   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x080064e4   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x080064e4   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x080064f8   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800650c   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800650c   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800651f   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08006534   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08006534   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08006570   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080065e8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080065ec   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080065f4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08006600   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08006602   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08006603   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08006604   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08006604   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08006608   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08006610   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08006714   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        5  main.o(.data)
    ad9854_status                            0x20000004   Data           1  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000001c   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000001c   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x2000002c   Section       25  systick.o(.data)
    s_delay_counter                          0x20000040   Data           4  systick.o(.data)
    s_dwt_initialized                        0x20000044   Data           1  systick.o(.data)
    .data                                    0x20000045   Section        2  usart.o(.data)
    .data                                    0x20000048   Section       64  ad9854.o(.data)
    freq_word                                0x20000048   Data           6  ad9854.o(.data)
    g_control_params                         0x20000050   Data          56  ad9854.o(.data)
    .bss                                     0x20000088   Section       56  main.o(.bss)
    control_params                           0x20000088   Data          56  main.o(.bss)
    .bss                                     0x200000c0   Section       16  systick.o(.bss)
    .bss                                     0x200000d0   Section     3684  usart.o(.bss)
    s_tx_buffer                              0x20000134   Data        2048  usart.o(.bss)
    s_rx_buffer                              0x20000934   Data        1024  usart.o(.bss)
    s_printf_buffer                          0x20000d34   Data         512  usart.o(.bss)
    .bss                                     0x20000f34   Section      404  external_control.o(.bss)
    tjc_screen_ctrl                          0x20000f34   Data         404  external_control.o(.bss)
    .bss                                     0x200010c8   Section       96  libspace.o(.bss)
    HEAP                                     0x20001128   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20001128   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20001328   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20001328   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20001728   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    vsnprintf                                0x08000315   Thumb Code    48  vsnprintf.o(.text)
    __2snprintf                              0x08000349   Thumb Code    50  __2snprintf.o(.text)
    __printf                                 0x08000381   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strlen                                   0x08000509   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy4                          0x08000547   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000547   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000547   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x0800058f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x080005ab   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080005ab   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080005ab   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080005af   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080005f9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080005fb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080005fd   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x080005ff   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000609   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08000615   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000641   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000663   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000675   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000687   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080006d9   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000751   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x08000779   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x0800092b   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000ba3   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000bc9   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000bd3   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000be5   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000ca1   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000d1d   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000d5f   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000d77   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000d8d   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000de3   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000dff   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000e0b   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __rt_locale                              0x08000e21   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08000e29   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000e29   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000e29   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000e31   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000ebb   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_hex_real                      0x08000f6d   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x08001269   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_cs_common                        0x080012e9   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080012fd   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800130d   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001315   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001329   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001339   Thumb Code     8  _printf_wchar.o(.text)
    _btod_etento                             0x08001341   Thumb Code   224  bigflt0.o(.text)
    _wcrtomb                                 0x08001425   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x08001465   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001465   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001465   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x0800146d   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080014b9   Thumb Code    16  rt_ctype_table.o(.text)
    exit                                     0x080014c9   Thumb Code    18  exit.o(.text)
    strcmp                                   0x080014dd   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x0800155d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001569   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001569   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800156b   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x0800156b   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080015a9   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080015ef   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800164f   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001987   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001a63   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001a8d   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001ab7   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AD9854_CalculateModelCircuitGain         0x08001d4d   Thumb Code   204  ad9854.o(i.AD9854_CalculateModelCircuitGain)
    AD9854_CalculateRequiredOutput           0x08001e41   Thumb Code   114  ad9854.o(i.AD9854_CalculateRequiredOutput)
    AD9854_CalculateRequiredOutputWithModel  0x08001ec5   Thumb Code    68  ad9854.o(i.AD9854_CalculateRequiredOutputWithModel)
    AD9854_Delay_us                          0x08001f09   Thumb Code    20  ad9854.o(i.AD9854_Delay_us)
    AD9854_EnableModelCircuit                0x08001f1d   Thumb Code   134  ad9854.o(i.AD9854_EnableModelCircuit)
    AD9854_EnableOutput                      0x08001fa9   Thumb Code    28  ad9854.o(i.AD9854_EnableOutput)
    AD9854_GPIO_Init                         0x08002035   Thumb Code   156  ad9854.o(i.AD9854_GPIO_Init)
    AD9854_GetControlParams                  0x080020dd   Thumb Code    24  ad9854.o(i.AD9854_GetControlParams)
    AD9854_Init                              0x080020f9   Thumb Code    84  ad9854.o(i.AD9854_Init)
    AD9854_InitControlInterface              0x08002155   Thumb Code    28  ad9854.o(i.AD9854_InitControlInterface)
    AD9854_Reset                             0x08002171   Thumb Code    32  ad9854.o(i.AD9854_Reset)
    AD9854_SetTargetAmplitude                0x08002231   Thumb Code   164  ad9854.o(i.AD9854_SetTargetAmplitude)
    AD9854_SetTargetFrequency                0x080022e9   Thumb Code   144  ad9854.o(i.AD9854_SetTargetFrequency)
    AD9854_System_Init                       0x0800238d   Thumb Code    28  main.o(i.AD9854_System_Init)
    AD9854_Update                            0x080023ad   Thumb Code    32  ad9854.o(i.AD9854_Update)
    AD9854_ValidateParams                    0x080023d1   Thumb Code   218  ad9854.o(i.AD9854_ValidateParams)
    AD9854_VppToAmplitudeCode                0x080024dd   Thumb Code    74  ad9854.o(i.AD9854_VppToAmplitudeCode)
    AD9854_WriteReg                          0x08002561   Thumb Code   120  ad9854.o(i.AD9854_WriteReg)
    BSP_Init                                 0x080025e1   Thumb Code    52  bsp.o(i.BSP_Init)
    BusFault_Handler                         0x08002615   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream2_IRQHandler                  0x08002619   Thumb Code    92  usart.o(i.DMA2_Stream2_IRQHandler)
    DMA2_Stream7_IRQHandler                  0x08002689   Thumb Code    80  usart.o(i.DMA2_Stream7_IRQHandler)
    DMA_ClearITPendingBit                    0x080026ed   Thumb Code   216  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x080027ed   Thumb Code   182  stm32f4xx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x080028c5   Thumb Code   462  stm32f4xx_dma.o(i.DMA_DeInit)
    DMA_GetCurrDataCounter                   0x08002ac1   Thumb Code   146  stm32f4xx_dma.o(i.DMA_GetCurrDataCounter)
    DMA_GetITStatus                          0x08002b75   Thumb Code   498  stm32f4xx_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x08002e21   Thumb Code   238  stm32f4xx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08002f31   Thumb Code   658  stm32f4xx_dma.o(i.DMA_Init)
    DMA_SetCurrDataCounter                   0x080031e9   Thumb Code   146  stm32f4xx_dma.o(i.DMA_SetCurrDataCounter)
    DWT_Init                                 0x0800329d   Thumb Code    78  systick.o(i.DWT_Init)
    DebugMon_Handler                         0x080032f9   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080032fd   Thumb Code    40  systick.o(i.Delay_ms)
    EXTI0_IRQHandler                         0x08003329   Thumb Code     8  stm32f4xx_it.o(i.EXTI0_IRQHandler)
    EXTI0_IRQHandler_Internal                0x08003331   Thumb Code     2  main.o(i.EXTI0_IRQHandler_Internal)
    ExternalControl_Process                  0x0800334d   Thumb Code     8  external_control.o(i.ExternalControl_Process)
    FLASH_SetLatency                         0x08003355   Thumb Code    84  stm32f4xx_flash.o(i.FLASH_SetLatency)
    GPIO_Init                                0x080033c9   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08003571   Thumb Code   352  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadOutputDataBit                   0x08003719   Thumb Code   194  stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit)
    GPIO_ResetBits                           0x08003825   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x080038d5   Thumb Code   104  stm32f4xx_gpio.o(i.GPIO_SetBits)
    GPIO_ToggleBits                          0x08003985   Thumb Code    92  stm32f4xx_gpio.o(i.GPIO_ToggleBits)
    HardFault_Handler                        0x08003a29   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08003a2d   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08003a31   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08003a35   Thumb Code   164  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08003b1d   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x08003b21   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08003b89   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08003bed   Thumb Code    70  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_DeInit                               0x08003c55   Thumb Code    82  stm32f4xx_rcc.o(i.RCC_DeInit)
    RCC_GetClocksFreq                        0x08003cb9   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    RCC_GetFlagStatus                        0x08003da1   Thumb Code   134  stm32f4xx_rcc.o(i.RCC_GetFlagStatus)
    RCC_GetSYSCLKSource                      0x08003e45   Thumb Code    10  stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource)
    RCC_HCLKConfig                           0x08003e55   Thumb Code    66  stm32f4xx_rcc.o(i.RCC_HCLKConfig)
    RCC_HSEConfig                            0x08003eb5   Thumb Code    38  stm32f4xx_rcc.o(i.RCC_HSEConfig)
    RCC_PCLK1Config                          0x08003ef9   Thumb Code    58  stm32f4xx_rcc.o(i.RCC_PCLK1Config)
    RCC_PCLK2Config                          0x08003f51   Thumb Code    60  stm32f4xx_rcc.o(i.RCC_PCLK2Config)
    RCC_PLLCmd                               0x08003fa9   Thumb Code    28  stm32f4xx_rcc.o(i.RCC_PLLCmd)
    RCC_PLLConfig                            0x08003fe1   Thumb Code   154  stm32f4xx_rcc.o(i.RCC_PLLConfig)
    RCC_SYSCLKConfig                         0x08004099   Thumb Code    42  stm32f4xx_rcc.o(i.RCC_SYSCLKConfig)
    RCC_WaitForHSEStartUp                    0x080040e1   Thumb Code    56  stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp)
    SVC_Handler                              0x08004129   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_GetTick                          0x08004219   Thumb Code     6  systick.o(i.SysTick_GetTick)
    SysTick_Handler                          0x08004225   Thumb Code    22  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Handler_Internal                 0x08004241   Thumb Code    42  systick.o(i.SysTick_Handler_Internal)
    SysTick_Init                             0x08004279   Thumb Code   114  systick.o(i.SysTick_Init)
    SysTick_ResetStats                       0x080042f5   Thumb Code    22  systick.o(i.SysTick_ResetStats)
    SystemClock_Config                       0x08004375   Thumb Code   106  main.o(i.SystemClock_Config)
    SystemInit                               0x080043e1   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM6_DAC_IRQHandler                      0x08004449   Thumb Code     2  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TJC_InitDisplay                          0x0800444d   Thumb Code    86  external_control.o(i.TJC_InitDisplay)
    TJC_ParseCommand                         0x08004511   Thumb Code     6  external_control.o(i.TJC_ParseCommand)
    TJC_Screen_Init                          0x08004519   Thumb Code    58  external_control.o(i.TJC_Screen_Init)
    TJC_Screen_ProcessData                   0x08004559   Thumb Code    38  external_control.o(i.TJC_Screen_ProcessData)
    TJC_SendFloat                            0x08004585   Thumb Code    80  external_control.o(i.TJC_SendFloat)
    TJC_SendText                             0x080045e5   Thumb Code    26  external_control.o(i.TJC_SendText)
    TJC_SendValue                            0x08004611   Thumb Code    24  external_control.o(i.TJC_SendValue)
    TJC_SetAmplitude                         0x08004639   Thumb Code   142  external_control.o(i.TJC_SetAmplitude)
    TJC_SetFrequency                         0x08004705   Thumb Code   168  external_control.o(i.TJC_SetFrequency)
    TJC_SetOutput                            0x080047f5   Thumb Code    36  external_control.o(i.TJC_SetOutput)
    TimingDelay_Decrement                    0x08004841   Thumb Code     2  main.o(i.TimingDelay_Decrement)
    USART1_IRQHandler                        0x08004845   Thumb Code   138  usart.o(i.USART1_IRQHandler)
    USART1_Init                              0x080048e1   Thumb Code   200  usart.o(i.USART1_Init)
    USART_ClearITPendingBit                  0x080049c1   Thumb Code   188  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08004ac1   Thumb Code   120  stm32f4xx_usart.o(i.USART_Cmd)
    USART_DMACmd                             0x08004b7d   Thumb Code   138  stm32f4xx_usart.o(i.USART_DMACmd)
    USART_GetFlagStatus                      0x08004d59   Thumb Code   194  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08004e61   Thumb Code   302  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08004fd5   Thumb Code   284  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08005135   Thumb Code   482  stm32f4xx_usart.o(i.USART_Init)
    USART_Module_SendData                    0x08005361   Thumb Code    50  usart.o(i.USART_Module_SendData)
    USART_Printf                             0x080053f1   Thumb Code    46  usart.o(i.USART_Printf)
    USART_ReceiveByte                        0x08005425   Thumb Code    72  usart.o(i.USART_ReceiveByte)
    USART_ReceiveData                        0x08005475   Thumb Code    84  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_ResetStats                         0x0800550d   Thumb Code    12  usart.o(i.USART_ResetStats)
    USART_SendByte                           0x0800551d   Thumb Code    64  usart.o(i.USART_SendByte)
    USART_SendData                           0x08005565   Thumb Code   104  stm32f4xx_usart.o(i.USART_SendData)
    USART_SendString                         0x08005611   Thumb Code    28  usart.o(i.USART_SendString)
    UsageFault_Handler                       0x0800562d   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08005631   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_sqrt                            0x08005661   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    _is_digit                                0x080056db   Thumb Code    14  __printf_wp.o(i._is_digit)
    assert_failed                            0x080056e9   Thumb Code     4  main.o(i.assert_failed)
    main                                     0x080056ed   Thumb Code   282  main.o(i.main)
    _get_lc_numeric                          0x0800582d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08005859   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_dadd                             0x08005885   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08005885   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x080059d5   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x080059ed   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080059ed   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08005c9d   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08005c9d   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_cdcmple                          0x08005cf9   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08005cf9   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08005d5b   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08005d71   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08005d71   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08005ec5   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08005f61   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x08005f6d   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x08005f6d   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x08005fd9   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08005fd9   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08005ff1   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08006189   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08006189   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800635d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800635d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x080063b3   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800643f   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08006447   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08006447   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __aeabi_d2ulz                            0x08006449   Thumb Code     0  dfixull.o(x$fpl$llufromd)
    _ll_ufrom_d                              0x08006449   Thumb Code   120  dfixull.o(x$fpl$llufromd)
    _printf_fp_dec                           0x080064c1   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x080064c5   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x080064c8   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080065c8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080065e8   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08006611   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f4xx.o(.data)
    g_systick_counter                        0x2000002c   Data           4  systick.o(.data)
    g_system_uptime_ms                       0x20000030   Data           4  systick.o(.data)
    g_systick_cal                            0x20000034   Data          12  systick.o(.data)
    g_usart_tx_complete_flag                 0x20000045   Data           1  usart.o(.data)
    g_usart_rx_complete_flag                 0x20000046   Data           1  usart.o(.data)
    g_systick_stats                          0x200000c0   Data          16  systick.o(.bss)
    g_usart1_handle                          0x200000d0   Data         100  usart.o(.bss)
    __libspace_start                         0x200010c8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20001128   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000679c, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006714, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         2147  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         2511    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         2513    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         2515    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         2136    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         2252    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         2254    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         2135    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         2259    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         2260    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         2261    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         2266    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         2256    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         2257    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         2258    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         2255    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         2253    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         2263    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         2264    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         2265    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         2270    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         2271    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         2267    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         2250    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         2251    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         2268    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         2269    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         2262    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         2381    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         2382    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         2385    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         2388    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         2390    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         2392    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         2393    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x00000000   Code   RO         2395    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x0000000c   Code   RO         2396    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         2397    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         2399    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x0000000a   Code   RO         2400    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2401    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2403    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2405    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2407    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2409    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2411    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2413    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2415    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2419    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2421    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2423    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         2425    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         2426    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         2459    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         2470    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         2472    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         2475    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         2478    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         2480    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         2483    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         2484    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         2197    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         2292    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         2304    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         2294    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         2295    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         2297    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         2298    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         2427    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         2437    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         2438    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         2439    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f40_41xxx.o
    0x08000314   0x08000314   0x00000034   Code   RO         2107    .text               c_w.l(vsnprintf.o)
    0x08000348   0x08000348   0x00000038   Code   RO         2109    .text               c_w.l(__2snprintf.o)
    0x08000380   0x08000380   0x00000188   Code   RO         2132    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000508   0x08000508   0x0000003e   Code   RO         2137    .text               c_w.l(strlen.o)
    0x08000546   0x08000546   0x00000064   Code   RO         2141    .text               c_w.l(rt_memcpy_w.o)
    0x080005aa   0x080005aa   0x0000004e   Code   RO         2143    .text               c_w.l(rt_memclr_w.o)
    0x080005f8   0x080005f8   0x00000006   Code   RO         2145    .text               c_w.l(heapauxi.o)
    0x080005fe   0x080005fe   0x00000016   Code   RO         2198    .text               c_w.l(_rserrno.o)
    0x08000614   0x08000614   0x0000004e   Code   RO         2200    .text               c_w.l(_printf_pad.o)
    0x08000662   0x08000662   0x00000024   Code   RO         2202    .text               c_w.l(_printf_truncate.o)
    0x08000686   0x08000686   0x00000052   Code   RO         2204    .text               c_w.l(_printf_str.o)
    0x080006d8   0x080006d8   0x00000078   Code   RO         2206    .text               c_w.l(_printf_dec.o)
    0x08000750   0x08000750   0x00000028   Code   RO         2208    .text               c_w.l(_printf_charcount.o)
    0x08000778   0x08000778   0x0000041e   Code   RO         2210    .text               c_w.l(_printf_fp_dec.o)
    0x08000b96   0x08000b96   0x00000002   PAD
    0x08000b98   0x08000b98   0x00000030   Code   RO         2212    .text               c_w.l(_printf_char_common.o)
    0x08000bc8   0x08000bc8   0x0000000a   Code   RO         2214    .text               c_w.l(_sputc.o)
    0x08000bd2   0x08000bd2   0x00000010   Code   RO         2216    .text               c_w.l(_snputc.o)
    0x08000be2   0x08000be2   0x00000002   PAD
    0x08000be4   0x08000be4   0x000000bc   Code   RO         2218    .text               c_w.l(_printf_wctomb.o)
    0x08000ca0   0x08000ca0   0x0000007c   Code   RO         2221    .text               c_w.l(_printf_longlong_dec.o)
    0x08000d1c   0x08000d1c   0x00000070   Code   RO         2227    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000d8c   0x08000d8c   0x00000094   Code   RO         2247    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000e20   0x08000e20   0x00000008   Code   RO         2309    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000e28   0x08000e28   0x00000008   Code   RO         2314    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000e30   0x08000e30   0x0000008a   Code   RO         2316    .text               c_w.l(lludiv10.o)
    0x08000eba   0x08000eba   0x000000b2   Code   RO         2318    .text               c_w.l(_printf_intcommon.o)
    0x08000f6c   0x08000f6c   0x000002fc   Code   RO         2320    .text               c_w.l(_printf_fp_hex.o)
    0x08001268   0x08001268   0x00000080   Code   RO         2323    .text               c_w.l(_printf_fp_infnan.o)
    0x080012e8   0x080012e8   0x0000002c   Code   RO         2327    .text               c_w.l(_printf_char.o)
    0x08001314   0x08001314   0x0000002c   Code   RO         2329    .text               c_w.l(_printf_wchar.o)
    0x08001340   0x08001340   0x000000e4   Code   RO         2331    .text               c_w.l(bigflt0.o)
    0x08001424   0x08001424   0x00000040   Code   RO         2356    .text               c_w.l(_wcrtomb.o)
    0x08001464   0x08001464   0x00000008   Code   RO         2365    .text               c_w.l(libspace.o)
    0x0800146c   0x0800146c   0x0000004a   Code   RO         2368    .text               c_w.l(sys_stackheap_outer.o)
    0x080014b6   0x080014b6   0x00000002   PAD
    0x080014b8   0x080014b8   0x00000010   Code   RO         2370    .text               c_w.l(rt_ctype_table.o)
    0x080014c8   0x080014c8   0x00000012   Code   RO         2372    .text               c_w.l(exit.o)
    0x080014da   0x080014da   0x00000002   PAD
    0x080014dc   0x080014dc   0x00000080   Code   RO         2374    .text               c_w.l(strcmpv7m.o)
    0x0800155c   0x0800155c   0x0000000c   Code   RO         2449    .text               c_w.l(sys_exit.o)
    0x08001568   0x08001568   0x00000002   Code   RO         2460    .text               c_w.l(use_no_semi.o)
    0x0800156a   0x0800156a   0x00000000   Code   RO         2462    .text               c_w.l(indicate_semi.o)
    0x0800156a   0x0800156a   0x0000003e   Code   RO         2334    CL$$btod_d2e        c_w.l(btod.o)
    0x080015a8   0x080015a8   0x00000046   Code   RO         2336    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080015ee   0x080015ee   0x00000060   Code   RO         2335    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800164e   0x0800164e   0x00000338   Code   RO         2344    CL$$btod_div_common  c_w.l(btod.o)
    0x08001986   0x08001986   0x000000dc   Code   RO         2341    CL$$btod_e2e        c_w.l(btod.o)
    0x08001a62   0x08001a62   0x0000002a   Code   RO         2338    CL$$btod_ediv       c_w.l(btod.o)
    0x08001a8c   0x08001a8c   0x0000002a   Code   RO         2337    CL$$btod_emul       c_w.l(btod.o)
    0x08001ab6   0x08001ab6   0x00000244   Code   RO         2343    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001cfa   0x08001cfa   0x00000002   PAD
    0x08001cfc   0x08001cfc   0x00000050   Code   RO         1805    i.AD9854_ApplyCurrentParams  ad9854.o
    0x08001d4c   0x08001d4c   0x000000f4   Code   RO         1806    i.AD9854_CalculateModelCircuitGain  ad9854.o
    0x08001e40   0x08001e40   0x00000084   Code   RO         1807    i.AD9854_CalculateRequiredOutput  ad9854.o
    0x08001ec4   0x08001ec4   0x00000044   Code   RO         1808    i.AD9854_CalculateRequiredOutputWithModel  ad9854.o
    0x08001f08   0x08001f08   0x00000014   Code   RO         1809    i.AD9854_Delay_us   ad9854.o
    0x08001f1c   0x08001f1c   0x0000008c   Code   RO         1810    i.AD9854_EnableModelCircuit  ad9854.o
    0x08001fa8   0x08001fa8   0x00000020   Code   RO         1811    i.AD9854_EnableOutput  ad9854.o
    0x08001fc8   0x08001fc8   0x0000006c   Code   RO         1812    i.AD9854_FrequencyToWord  ad9854.o
    0x08002034   0x08002034   0x000000a8   Code   RO         1813    i.AD9854_GPIO_Init  ad9854.o
    0x080020dc   0x080020dc   0x0000001c   Code   RO         1814    i.AD9854_GetControlParams  ad9854.o
    0x080020f8   0x080020f8   0x0000005c   Code   RO         1815    i.AD9854_Init       ad9854.o
    0x08002154   0x08002154   0x0000001c   Code   RO         1816    i.AD9854_InitControlInterface  ad9854.o
    0x08002170   0x08002170   0x00000024   Code   RO         1818    i.AD9854_Reset      ad9854.o
    0x08002194   0x08002194   0x00000036   Code   RO         1819    i.AD9854_SetAmplitude  ad9854.o
    0x080021ca   0x080021ca   0x00000002   PAD
    0x080021cc   0x080021cc   0x00000064   Code   RO         1820    i.AD9854_SetFrequency  ad9854.o
    0x08002230   0x08002230   0x000000b8   Code   RO         1822    i.AD9854_SetTargetAmplitude  ad9854.o
    0x080022e8   0x080022e8   0x000000a4   Code   RO         1823    i.AD9854_SetTargetFrequency  ad9854.o
    0x0800238c   0x0800238c   0x00000020   Code   RO           13    i.AD9854_System_Init  main.o
    0x080023ac   0x080023ac   0x00000024   Code   RO         1824    i.AD9854_Update     ad9854.o
    0x080023d0   0x080023d0   0x0000010c   Code   RO         1825    i.AD9854_ValidateParams  ad9854.o
    0x080024dc   0x080024dc   0x0000005c   Code   RO         1826    i.AD9854_VppToAmplitudeCode  ad9854.o
    0x08002538   0x08002538   0x00000028   Code   RO         1827    i.AD9854_WriteFrequencyWord  ad9854.o
    0x08002560   0x08002560   0x00000080   Code   RO         1828    i.AD9854_WriteReg   ad9854.o
    0x080025e0   0x080025e0   0x00000034   Code   RO          351    i.BSP_Init          bsp.o
    0x08002614   0x08002614   0x00000004   Code   RO          215    i.BusFault_Handler  stm32f4xx_it.o
    0x08002618   0x08002618   0x00000070   Code   RO         1627    i.DMA2_Stream2_IRQHandler  usart.o
    0x08002688   0x08002688   0x00000064   Code   RO         1628    i.DMA2_Stream7_IRQHandler  usart.o
    0x080026ec   0x080026ec   0x00000100   Code   RO         1355    i.DMA_ClearITPendingBit  stm32f4xx_dma.o
    0x080027ec   0x080027ec   0x000000d8   Code   RO         1356    i.DMA_Cmd           stm32f4xx_dma.o
    0x080028c4   0x080028c4   0x000001fc   Code   RO         1357    i.DMA_DeInit        stm32f4xx_dma.o
    0x08002ac0   0x08002ac0   0x000000b4   Code   RO         1362    i.DMA_GetCurrDataCounter  stm32f4xx_dma.o
    0x08002b74   0x08002b74   0x000002ac   Code   RO         1366    i.DMA_GetITStatus   stm32f4xx_dma.o
    0x08002e20   0x08002e20   0x00000110   Code   RO         1367    i.DMA_ITConfig      stm32f4xx_dma.o
    0x08002f30   0x08002f30   0x000002b8   Code   RO         1368    i.DMA_Init          stm32f4xx_dma.o
    0x080031e8   0x080031e8   0x000000b4   Code   RO         1371    i.DMA_SetCurrDataCounter  stm32f4xx_dma.o
    0x0800329c   0x0800329c   0x0000005c   Code   RO         1489    i.DWT_Init          systick.o
    0x080032f8   0x080032f8   0x00000002   Code   RO          216    i.DebugMon_Handler  stm32f4xx_it.o
    0x080032fa   0x080032fa   0x00000002   PAD
    0x080032fc   0x080032fc   0x0000002c   Code   RO         1490    i.Delay_ms          systick.o
    0x08003328   0x08003328   0x00000008   Code   RO          217    i.EXTI0_IRQHandler  stm32f4xx_it.o
    0x08003330   0x08003330   0x00000002   Code   RO           14    i.EXTI0_IRQHandler_Internal  main.o
    0x08003332   0x08003332   0x00000002   PAD
    0x08003334   0x08003334   0x00000018   Code   RO           15    i.Error_Handler     main.o
    0x0800334c   0x0800334c   0x00000008   Code   RO         1975    i.ExternalControl_Process  external_control.o
    0x08003354   0x08003354   0x00000074   Code   RO          935    i.FLASH_SetLatency  stm32f4xx_flash.o
    0x080033c8   0x080033c8   0x000001a8   Code   RO          450    i.GPIO_Init         stm32f4xx_gpio.o
    0x08003570   0x08003570   0x000001a8   Code   RO          451    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08003718   0x08003718   0x0000010c   Code   RO          456    i.GPIO_ReadOutputDataBit  stm32f4xx_gpio.o
    0x08003824   0x08003824   0x000000b0   Code   RO          457    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x080038d4   0x080038d4   0x000000b0   Code   RO          458    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08003984   0x08003984   0x000000a4   Code   RO          460    i.GPIO_ToggleBits   stm32f4xx_gpio.o
    0x08003a28   0x08003a28   0x00000004   Code   RO          218    i.HardFault_Handler  stm32f4xx_it.o
    0x08003a2c   0x08003a2c   0x00000004   Code   RO          219    i.MemManage_Handler  stm32f4xx_it.o
    0x08003a30   0x08003a30   0x00000002   Code   RO          220    i.NMI_Handler       stm32f4xx_it.o
    0x08003a32   0x08003a32   0x00000002   PAD
    0x08003a34   0x08003a34   0x000000c0   Code   RO          375    i.NVIC_Init         misc.o
    0x08003af4   0x08003af4   0x00000028   Code   RO         1493    i.NVIC_SetPriority  systick.o
    0x08003b1c   0x08003b1c   0x00000002   Code   RO          221    i.PendSV_Handler    stm32f4xx_it.o
    0x08003b1e   0x08003b1e   0x00000002   PAD
    0x08003b20   0x08003b20   0x00000068   Code   RO          557    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08003b88   0x08003b88   0x00000064   Code   RO          566    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08003bec   0x08003bec   0x00000068   Code   RO          569    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08003c54   0x08003c54   0x00000064   Code   RO          577    i.RCC_DeInit        stm32f4xx_rcc.o
    0x08003cb8   0x08003cb8   0x000000e8   Code   RO          578    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08003da0   0x08003da0   0x000000a4   Code   RO          579    i.RCC_GetFlagStatus  stm32f4xx_rcc.o
    0x08003e44   0x08003e44   0x00000010   Code   RO          581    i.RCC_GetSYSCLKSource  stm32f4xx_rcc.o
    0x08003e54   0x08003e54   0x00000060   Code   RO          582    i.RCC_HCLKConfig    stm32f4xx_rcc.o
    0x08003eb4   0x08003eb4   0x00000044   Code   RO          583    i.RCC_HSEConfig     stm32f4xx_rcc.o
    0x08003ef8   0x08003ef8   0x00000058   Code   RO          593    i.RCC_PCLK1Config   stm32f4xx_rcc.o
    0x08003f50   0x08003f50   0x00000058   Code   RO          594    i.RCC_PCLK2Config   stm32f4xx_rcc.o
    0x08003fa8   0x08003fa8   0x00000038   Code   RO          595    i.RCC_PLLCmd        stm32f4xx_rcc.o
    0x08003fe0   0x08003fe0   0x000000b8   Code   RO          596    i.RCC_PLLConfig     stm32f4xx_rcc.o
    0x08004098   0x08004098   0x00000048   Code   RO          607    i.RCC_SYSCLKConfig  stm32f4xx_rcc.o
    0x080040e0   0x080040e0   0x00000038   Code   RO          609    i.RCC_WaitForHSEStartUp  stm32f4xx_rcc.o
    0x08004118   0x08004118   0x00000010   Code   RO         1629    i.RingBuffer_Init   usart.o
    0x08004128   0x08004128   0x00000002   Code   RO          222    i.SVC_Handler       stm32f4xx_it.o
    0x0800412a   0x0800412a   0x00000002   PAD
    0x0800412c   0x0800412c   0x000000ec   Code   RO          310    i.SetSysClock       system_stm32f4xx.o
    0x08004218   0x08004218   0x0000000c   Code   RO         1497    i.SysTick_GetTick   systick.o
    0x08004224   0x08004224   0x0000001c   Code   RO          223    i.SysTick_Handler   stm32f4xx_it.o
    0x08004240   0x08004240   0x00000038   Code   RO         1500    i.SysTick_Handler_Internal  systick.o
    0x08004278   0x08004278   0x0000007c   Code   RO         1501    i.SysTick_Init      systick.o
    0x080042f4   0x080042f4   0x00000020   Code   RO         1504    i.SysTick_ResetStats  systick.o
    0x08004314   0x08004314   0x00000060   Code   RO         1506    i.SysTick_UpdateStats  systick.o
    0x08004374   0x08004374   0x0000006a   Code   RO           16    i.SystemClock_Config  main.o
    0x080043de   0x080043de   0x00000002   PAD
    0x080043e0   0x080043e0   0x00000068   Code   RO          312    i.SystemInit        system_stm32f4xx.o
    0x08004448   0x08004448   0x00000002   Code   RO          224    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x0800444a   0x0800444a   0x00000002   PAD
    0x0800444c   0x0800444c   0x000000c4   Code   RO         1978    i.TJC_InitDisplay   external_control.o
    0x08004510   0x08004510   0x00000006   Code   RO         1979    i.TJC_ParseCommand  external_control.o
    0x08004516   0x08004516   0x00000002   PAD
    0x08004518   0x08004518   0x00000040   Code   RO         1980    i.TJC_Screen_Init   external_control.o
    0x08004558   0x08004558   0x0000002c   Code   RO         1981    i.TJC_Screen_ProcessData  external_control.o
    0x08004584   0x08004584   0x00000060   Code   RO         1982    i.TJC_SendFloat     external_control.o
    0x080045e4   0x080045e4   0x0000002c   Code   RO         1984    i.TJC_SendText      external_control.o
    0x08004610   0x08004610   0x00000028   Code   RO         1985    i.TJC_SendValue     external_control.o
    0x08004638   0x08004638   0x000000cc   Code   RO         1986    i.TJC_SetAmplitude  external_control.o
    0x08004704   0x08004704   0x000000f0   Code   RO         1987    i.TJC_SetFrequency  external_control.o
    0x080047f4   0x080047f4   0x0000004c   Code   RO         1988    i.TJC_SetOutput     external_control.o
    0x08004840   0x08004840   0x00000002   Code   RO           17    i.TimingDelay_Decrement  main.o
    0x08004842   0x08004842   0x00000002   PAD
    0x08004844   0x08004844   0x0000009c   Code   RO         1632    i.USART1_IRQHandler  usart.o
    0x080048e0   0x080048e0   0x000000e0   Code   RO         1633    i.USART1_Init       usart.o
    0x080049c0   0x080049c0   0x00000100   Code   RO         1160    i.USART_ClearITPendingBit  stm32f4xx_usart.o
    0x08004ac0   0x08004ac0   0x000000bc   Code   RO         1163    i.USART_Cmd         stm32f4xx_usart.o
    0x08004b7c   0x08004b7c   0x000000d0   Code   RO         1164    i.USART_DMACmd      stm32f4xx_usart.o
    0x08004c4c   0x08004c4c   0x000000a8   Code   RO         1634    i.USART_DMA_Config  usart.o
    0x08004cf4   0x08004cf4   0x00000064   Code   RO         1635    i.USART_GPIO_Config  usart.o
    0x08004d58   0x08004d58   0x00000108   Code   RO         1166    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x08004e60   0x08004e60   0x00000174   Code   RO         1167    i.USART_GetITStatus  stm32f4xx_usart.o
    0x08004fd4   0x08004fd4   0x00000160   Code   RO         1169    i.USART_ITConfig    stm32f4xx_usart.o
    0x08005134   0x08005134   0x0000022c   Code   RO         1170    i.USART_Init        stm32f4xx_usart.o
    0x08005360   0x08005360   0x00000032   Code   RO         1639    i.USART_Module_SendData  usart.o
    0x08005392   0x08005392   0x0000005c   Code   RO         1640    i.USART_NVIC_Config  usart.o
    0x080053ee   0x080053ee   0x00000002   PAD
    0x080053f0   0x080053f0   0x00000034   Code   RO         1641    i.USART_Printf      usart.o
    0x08005424   0x08005424   0x00000050   Code   RO         1642    i.USART_ReceiveByte  usart.o
    0x08005474   0x08005474   0x00000098   Code   RO         1177    i.USART_ReceiveData  stm32f4xx_usart.o
    0x0800550c   0x0800550c   0x00000010   Code   RO         1643    i.USART_ResetStats  usart.o
    0x0800551c   0x0800551c   0x00000048   Code   RO         1644    i.USART_SendByte    usart.o
    0x08005564   0x08005564   0x000000ac   Code   RO         1180    i.USART_SendData    stm32f4xx_usart.o
    0x08005610   0x08005610   0x0000001c   Code   RO         1647    i.USART_SendString  usart.o
    0x0800562c   0x0800562c   0x00000004   Code   RO          225    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005630   0x08005630   0x00000030   Code   RO         2363    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08005660   0x08005660   0x0000007a   Code   RO         2185    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x080056da   0x080056da   0x0000000e   Code   RO         2125    i._is_digit         c_w.l(__printf_wp.o)
    0x080056e8   0x080056e8   0x00000004   Code   RO           18    i.assert_failed     main.o
    0x080056ec   0x080056ec   0x00000140   Code   RO           19    i.main              main.o
    0x0800582c   0x0800582c   0x0000002c   Code   RO         2359    locale$$code        c_w.l(lc_numeric_c.o)
    0x08005858   0x08005858   0x0000002c   Code   RO         2430    locale$$code        c_w.l(lc_ctype_c.o)
    0x08005884   0x08005884   0x00000150   Code   RO         2151    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x080059d4   0x080059d4   0x00000018   Code   RO         2272    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x080059ec   0x080059ec   0x000002b0   Code   RO         2158    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08005c9c   0x08005c9c   0x0000005a   Code   RO         2161    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x08005cf6   0x08005cf6   0x00000002   PAD
    0x08005cf8   0x08005cf8   0x00000078   Code   RO         2175    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08005d70   0x08005d70   0x00000154   Code   RO         2177    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08005ec4   0x08005ec4   0x0000009c   Code   RO         2274    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08005f60   0x08005f60   0x0000000c   Code   RO         2276    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08005f6c   0x08005f6c   0x0000006c   Code   RO         2179    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08005fd8   0x08005fd8   0x00000016   Code   RO         2152    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08005fee   0x08005fee   0x00000002   PAD
    0x08005ff0   0x08005ff0   0x00000198   Code   RO         2278    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08006188   0x08006188   0x000001d4   Code   RO         2153    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800635c   0x0800635c   0x00000056   Code   RO         2181    x$fpl$f2d           fz_wm.l(f2d.o)
    0x080063b2   0x080063b2   0x0000008c   Code   RO         2280    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800643e   0x0800643e   0x0000000a   Code   RO         2434    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08006448   0x08006448   0x00000078   Code   RO         2165    x$fpl$llufromd      fz_wm.l(dfixull.o)
    0x080064c0   0x080064c0   0x00000004   Code   RO         2183    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080064c4   0x080064c4   0x00000004   Code   RO         2284    x$fpl$printf2       fz_wm.l(printf2.o)
    0x080064c8   0x080064c8   0x00000000   Code   RO         2290    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080064c8   0x080064c8   0x00000011   Data   RO         2133    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080064d9   0x080064d9   0x00000003   PAD
    0x080064dc   0x080064dc   0x00000008   Data   RO         2219    .constdata          c_w.l(_printf_wctomb.o)
    0x080064e4   0x080064e4   0x00000028   Data   RO         2248    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800650c   0x0800650c   0x00000026   Data   RO         2321    .constdata          c_w.l(_printf_fp_hex.o)
    0x08006532   0x08006532   0x00000002   PAD
    0x08006534   0x08006534   0x00000094   Data   RO         2332    .constdata          c_w.l(bigflt0.o)
    0x080065c8   0x080065c8   0x00000020   Data   RO         2509    Region$$Table       anon$$obj.o
    0x080065e8   0x080065e8   0x0000001c   Data   RO         2358    locale$$data        c_w.l(lc_numeric_c.o)
    0x08006604   0x08006604   0x00000110   Data   RO         2429    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x0800679c, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006714, Size: 0x00001728, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08006714   0x00000005   Data   RW           21    .data               main.o
    0x20000005   0x08006719   0x00000003   PAD
    0x20000008   0x0800671c   0x00000014   Data   RW          313    .data               system_stm32f4xx.o
    0x2000001c   0x08006730   0x00000010   Data   RW          610    .data               stm32f4xx_rcc.o
    0x2000002c   0x08006740   0x00000019   Data   RW         1508    .data               systick.o
    0x20000045   0x08006759   0x00000002   Data   RW         1652    .data               usart.o
    0x20000047   0x0800675b   0x00000001   PAD
    0x20000048   0x0800675c   0x00000040   Data   RW         1829    .data               ad9854.o
    0x20000088        -       0x00000038   Zero   RW           20    .bss                main.o
    0x200000c0        -       0x00000010   Zero   RW         1507    .bss                systick.o
    0x200000d0        -       0x00000e64   Zero   RW         1651    .bss                usart.o
    0x20000f34        -       0x00000194   Zero   RW         1991    .bss                external_control.o
    0x200010c8        -       0x00000060   Zero   RW         2366    .bss                c_w.l(libspace.o)
    0x20001128        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f40_41xxx.o
    0x20001328        -       0x00000400   Zero   RW            1    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2242        266          0         64          0      17780   ad9854.o
        52          0          0          0          0        451   bsp.o
      1018        346          0          0        404       9107   external_control.o
       490         48          0          5         56      52850   main.o
       192         28          0          0          0     239280   misc.o
        64         26        392          0       1536        844   startup_stm32f40_41xxx.o
      2992        446          0          0          0      19757   stm32f4xx_dma.o
       116         32          0          0          0        603   stm32f4xx_flash.o
      1632        434          0          0          0      13278   stm32f4xx_gpio.o
        62          6          0          0          0       4859   stm32f4xx_it.o
      1528        376          0         16          0      26423   stm32f4xx_rcc.o
      2520        624          0          0          0      13418   stm32f4xx_usart.o
       340         32          0         20          0       1689   system_stm32f4xx.o
       496         82          0         25         16      32347   systick.o
      1266        130          0          2       3684      11732   usart.o

    ----------------------------------------------------------------------
     15032       <USER>        <GROUP>        136       5696     444418   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        22          0          0          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        56          6          0          0          0         88   __2snprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        90          4          0          0          0        140   dfixu.o
       120          4          0          0          0        152   dfixull.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o
       122          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     10376        <USER>        <GROUP>          0         96       7340   Library Totals
        18          0          5          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7052        276        551          0         96       4456   c_w.l
      3136        248          0          0          0       2612   fz_wm.l
       170          0          0          0          0        272   m_wm.l

    ----------------------------------------------------------------------
     10376        <USER>        <GROUP>          0         96       7340   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     25408       3400        980        136       5792     436514   Grand Totals
     25408       3400        980        136       5792     436514   ELF Image Totals
     25408       3400        980        136          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                26388 (  25.77kB)
    Total RW  Size (RW Data + ZI Data)              5928 (   5.79kB)
    Total ROM Size (Code + RO Data + RW Data)      26524 (  25.90kB)

==============================================================================

