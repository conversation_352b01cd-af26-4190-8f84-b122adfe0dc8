/**
  ******************************************************************************
  * @file    external_control.h
  * <AUTHOR> 第三问 外部控制接口
  * @version V1.0
  * @date    2024
  * @brief   AD9854外部控制接口头文件 - 支持串口屏和矩阵键盘
  ******************************************************************************
  * @attention
  * 
  * 本模块提供AD9854的外部控制接口，支持：
  * 1. 串口屏控制 - 图形化界面，实时参数调节
  * 2. 4x4矩阵键盘控制 - 数字输入，菜单导航
  * 3. 参数存储 - Flash保存/加载配置
  * 4. 状态监控 - 实时显示输出参数
  * 
  * 控制参数：
  * - 频率：1Hz ~ 150MHz (48位精度)
  * - 峰峰值：10mV ~ 5V (考虑后续电路增益)
  * - 增益系数：0.01 ~ 100 (后续电路补偿)
  * 
  ******************************************************************************
  */

#ifndef __EXTERNAL_CONTROL_H
#define __EXTERNAL_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "../Generation/ad9854.h"
#include <stdint.h>
#include <string.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 控制界面状态
 */
typedef enum {
    UI_STATE_MAIN_MENU = 0,      // 主菜单
    UI_STATE_FREQ_SETTING,       // 频率设置
    UI_STATE_AMPLITUDE_SETTING,  // 幅度设置
    UI_STATE_GAIN_SETTING,       // 增益设置
    UI_STATE_OUTPUT_CONTROL,     // 输出控制
    UI_STATE_SYSTEM_INFO,        // 系统信息
    UI_STATE_SAVE_LOAD           // 保存/加载
} UI_State_t;



// 陶晶池串口屏接收缓冲区大小
#define TJC_RX_BUFFER_SIZE      128
#define TJC_TX_BUFFER_SIZE      256

/**
 * @brief 陶晶池串口屏控制结构体
 */
typedef struct {
    uint8_t rx_buffer[TJC_RX_BUFFER_SIZE];  // 接收缓冲区
    uint8_t tx_buffer[TJC_TX_BUFFER_SIZE];  // 发送缓冲区
    uint16_t rx_index;           // 接收索引
    uint8_t command_ready;       // 命令就绪标志
    uint8_t current_page;        // 当前页面
    uint8_t initialized;         // 初始化标志
    float current_frequency;     // 当前频率 (MHz)
    float current_amplitude;     // 当前幅度 (V)
    uint8_t output_enabled;      // 输出使能状态
} TJC_Screen_Control_t;

/**
 * @brief 参数预设配置
 */
typedef struct {
    char name[16];               // 配置名称
    double frequency_hz;         // 频率
    double target_vpp_mv;        // 目标峰峰值
    double gain_factor;          // 增益系数
    uint8_t enable;              // 使能标志
} ParamPreset_t;

/* Exported constants --------------------------------------------------------*/



// 陶晶池串口屏控制参数
#define TJC_SCREEN_BAUDRATE     115200      // 陶晶池串口屏默认波特率
#define TJC_SCREEN_PORT         USART1      // 使用USART1
#define TJC_SCREEN_TX_PIN       GPIO_Pin_9  // PA9
#define TJC_SCREEN_RX_PIN       GPIO_Pin_10 // PA10



// 陶晶池串口屏命令结束符
#define TJC_CMD_END             "\xff\xff\xff"

// 陶晶池串口屏控件名称定义
#define TJC_OBJ_FREQ        "t_freq"        // 频率显示文本框
#define TJC_OBJ_AMP         "n_amp"         // 幅度显示数值框
#define TJC_OBJ_OUTPUT      "sw_output"     // 输出开关
#define TJC_OBJ_STATUS      "t_status"      // 状态显示文本框
#define TJC_OBJ_TITLE       "t_title"       // 标题文本框
#define TJC_OBJ_FREQ_UNIT   "t_freq_unit"   // 频率单位文本框
#define TJC_OBJ_AMP_UNIT    "t_amp_unit"    // 幅度单位文本框

// 陶晶池串口屏页面定义
#define TJC_PAGE_MAIN       0               // 主页面
#define TJC_PAGE_SETTINGS   1               // 设置页面
#define TJC_PAGE_INFO       2               // 信息页面

/* Exported variables --------------------------------------------------------*/
extern TJC_Screen_Control_t tjc_screen_ctrl;

/* Exported macro ------------------------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

// ==================== 陶晶池串口屏控制函数 ====================

/**
 * @brief  初始化陶晶池串口屏控制
 * @param  None
 * @retval uint8_t 初始化状态 (0-成功, 1-失败)
 */
uint8_t TJC_Screen_Init(void);

/**
 * @brief  陶晶池串口屏数据处理
 * @param  None
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t TJC_Screen_ProcessData(void);

/**
 * @brief  陶晶池串口屏命令解析
 * @param  data: 接收到的数据
 * @param  length: 数据长度
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t TJC_ParseCommand(uint8_t *data, uint8_t length);

/**
 * @brief  陶晶池串口屏发送文本到指定控件
 * @param  obj_name: 控件名称
 * @param  text: 要显示的文本
 * @retval None
 */
void TJC_SendText(const char* obj_name, const char* text);

/**
 * @brief  陶晶池串口屏发送数值到指定控件
 * @param  obj_name: 控件名称
 * @param  value: 要显示的数值
 * @retval None
 */
void TJC_SendValue(const char* obj_name, int value);

/**
 * @brief  陶晶池串口屏发送浮点数到指定控件
 * @param  obj_name: 控件名称
 * @param  value: 要显示的浮点数
 * @param  precision: 小数位数
 * @retval None
 */
void TJC_SendFloat(const char* obj_name, float value, uint8_t precision);

/**
 * @brief  陶晶池串口屏设置频率
 * @param  frequency_mhz: 频率值 (MHz)
 * @retval uint8_t 设置状态 (0-成功, 1-失败)
 */
uint8_t TJC_SetFrequency(float frequency_mhz);

/**
 * @brief  陶晶池串口屏设置幅度
 * @param  amplitude_v: 幅度值 (V)
 * @retval uint8_t 设置状态 (0-成功, 1-失败)
 */
uint8_t TJC_SetAmplitude(float amplitude_v);

/**
 * @brief  陶晶池串口屏切换输出开关
 * @param  enable: 1-使能, 0-禁用
 * @retval None
 */
void TJC_SetOutput(uint8_t enable);

/**
 * @brief  陶晶池串口屏更新所有显示
 * @param  None
 * @retval None
 */
void TJC_UpdateAllDisplay(void);

/**
 * @brief  陶晶池串口屏初始化显示
 * @param  None
 * @retval None
 */
void TJC_InitDisplay(void);

/**
 * @brief  陶晶池串口屏发送页面跳转命令
 * @param  page_id: 页面ID
 * @retval None
 */
void TJC_GotoPage(uint8_t page_id);

/**
 * @brief  陶晶池串口屏发送系统命令
 * @param  cmd: 系统命令
 * @retval None
 */
void TJC_SendSystemCommand(const char* cmd);

/**
 * @brief  陶晶池串口屏获取控件属性
 * @param  obj_name: 控件名称
 * @param  attr: 属性名称 (如 "val", "txt")
 * @retval None
 */
void TJC_GetAttribute(const char* obj_name, const char* attr);

/**
 * @brief  陶晶池串口屏设置控件可见性
 * @param  obj_name: 控件名称
 * @param  visible: 1-可见, 0-隐藏
 * @retval None
 */
void TJC_SetVisible(const char* obj_name, uint8_t visible);

// ==================== 通用控制函数 ====================

/**
 * @brief  外部控制主循环处理
 * @param  None
 * @retval None
 */
void ExternalControl_Process(void);

#ifdef __cplusplus
}
#endif

#endif /* __EXTERNAL_CONTROL_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
