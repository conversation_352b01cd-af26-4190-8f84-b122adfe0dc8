/**
  ******************************************************************************
  * @file    external_control.h
  * <AUTHOR> 第三问 外部控制接口
  * @version V1.0
  * @date    2024
  * @brief   AD9854外部控制接口头文件 - 支持串口屏和矩阵键盘
  ******************************************************************************
  * @attention
  * 
  * 本模块提供AD9854的外部控制接口，支持：
  * 1. 串口屏控制 - 图形化界面，实时参数调节
  * 2. 4x4矩阵键盘控制 - 数字输入，菜单导航
  * 3. 参数存储 - Flash保存/加载配置
  * 4. 状态监控 - 实时显示输出参数
  * 
  * 控制参数：
  * - 频率：1Hz ~ 150MHz (48位精度)
  * - 峰峰值：10mV ~ 5V (考虑后续电路增益)
  * - 增益系数：0.01 ~ 100 (后续电路补偿)
  * 
  ******************************************************************************
  */

#ifndef __EXTERNAL_CONTROL_H
#define __EXTERNAL_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "../Generation/ad9854.h"
#include <stdint.h>
#include <string.h>

/* Exported types ------------------------------------------------------------*/

/**
 * @brief 控制界面状态
 */
typedef enum {
    UI_STATE_MAIN_MENU = 0,      // 主菜单
    UI_STATE_FREQ_SETTING,       // 频率设置
    UI_STATE_AMPLITUDE_SETTING,  // 幅度设置
    UI_STATE_GAIN_SETTING,       // 增益设置
    UI_STATE_OUTPUT_CONTROL,     // 输出控制
    UI_STATE_SYSTEM_INFO,        // 系统信息
    UI_STATE_SAVE_LOAD           // 保存/加载
} UI_State_t;

/**
 * @brief 串口屏控制结构体
 */
typedef struct {
    uint8_t rx_buffer[UART_SCREEN_RX_BUFFER_SIZE];  // 接收缓冲区
    uint8_t tx_buffer[UART_SCREEN_TX_BUFFER_SIZE];  // 发送缓冲区
    uint16_t rx_status;          // 接收状态 (类似USART_RX_STA)
    uint8_t command_ready;       // 命令就绪标志
    UI_State_t current_state;    // 当前界面状态
    uint8_t initialized;         // 初始化标志
} UartScreen_Control_t;

/**
 * @brief 矩阵键盘控制结构体
 */
typedef struct {
    uint8_t key_matrix[4][4];    // 4x4键盘矩阵
    uint8_t last_key;            // 上次按键
    uint8_t current_key;         // 当前按键
    uint32_t key_press_time;     // 按键时间
    uint8_t key_state;           // 按键状态
    UI_State_t current_menu;     // 当前菜单
    char input_buffer[16];       // 数字输入缓冲区
    uint8_t input_index;         // 输入索引
} MatrixKeypad_Control_t;

/**
 * @brief 参数预设配置
 */
typedef struct {
    char name[16];               // 配置名称
    double frequency_hz;         // 频率
    double target_vpp_mv;        // 目标峰峰值
    double gain_factor;          // 增益系数
    uint8_t enable;              // 使能标志
} ParamPreset_t;

/* Exported constants --------------------------------------------------------*/

// 串口屏通信参数
#define UART_SCREEN_BAUDRATE    9600         // 修改为9600波特率，匹配您的串口屏
#define UART_SCREEN_PORT        USART1
#define UART_SCREEN_TX_PIN      GPIO_Pin_9   // PA9
#define UART_SCREEN_RX_PIN      GPIO_Pin_10  // PA10

// 串口屏接收缓冲区大小
#define UART_SCREEN_RX_BUFFER_SIZE  64
#define UART_SCREEN_TX_BUFFER_SIZE  128

// 矩阵键盘引脚定义 (预留，根据实际硬件调整)
#define KEYPAD_ROW_PORT         GPIOC
#define KEYPAD_COL_PORT         GPIOC
#define KEYPAD_ROW_PINS         (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3)
#define KEYPAD_COL_PINS         (GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7)

// 键盘按键定义
#define KEY_0                   0x00
#define KEY_1                   0x01
#define KEY_2                   0x02
#define KEY_3                   0x03
#define KEY_4                   0x04
#define KEY_5                   0x05
#define KEY_6                   0x06
#define KEY_7                   0x07
#define KEY_8                   0x08
#define KEY_9                   0x09
#define KEY_A                   0x0A  // 确认
#define KEY_B                   0x0B  // 取消
#define KEY_C                   0x0C  // 菜单
#define KEY_D                   0x0D  // 返回
#define KEY_STAR                0x0E  // *
#define KEY_HASH                0x0F  // #

// 预设配置数量
#define MAX_PRESETS             8

/* Exported macro ------------------------------------------------------------*/

// 串口屏命令格式 (基于您的HMI实现)
#define UART_CMD_SET_FREQ       '1'          // 设置频率命令
#define UART_CMD_SET_AMP        '2'          // 设置幅度命令
#define UART_CMD_ENABLE         '3'          // 使能输出命令
#define UART_CMD_DISABLE        '4'          // 禁用输出命令
#define UART_CMD_GET_STATUS     '5'          // 获取状态命令

// HMI显示控件名称
#define HMI_FREQ_TEXT           "t0.txt"     // 频率显示文本框
#define HMI_AMP_VALUE           "x0.val"     // 幅度显示数值框
#define HMI_STATUS_NUM          "n0.val"     // 状态显示数字框
#define HMI_ERROR_TEXT          "t1.txt"     // 错误信息文本框

/* Exported functions --------------------------------------------------------*/

// ==================== 串口屏控制函数 ====================

/**
 * @brief  初始化串口屏控制
 * @param  None
 * @retval uint8_t 初始化状态 (0-成功, 1-失败)
 */
uint8_t UartScreen_Init(void);

/**
 * @brief  处理串口屏接收数据
 * @param  None
 * @retval uint8_t 处理状态 (0-无命令, 1-有命令)
 */
uint8_t UartScreen_ProcessData(void);

/**
 * @brief  发送状态信息到串口屏
 * @param  params: 当前参数
 * @retval None
 */
void UartScreen_SendStatus(const AD9854_ControlParams_t *params);

/**
 * @brief  发送错误信息到串口屏
 * @param  error_code: 错误代码
 * @retval None
 */
void UartScreen_SendError(uint8_t error_code);

/**
 * @brief  HMI发送字符串到指定控件
 * @param  name: 控件名称
 * @param  showdata: 要显示的字符串
 * @retval None
 */
void HMI_SendString(const char* name, const char* showdata);

/**
 * @brief  HMI发送数字到指定控件
 * @param  name: 控件名称
 * @param  num: 要显示的数字
 * @retval None
 */
void HMI_SendNumber(const char* name, int num);

/**
 * @brief  HMI发送浮点数到指定控件
 * @param  name: 控件名称
 * @param  num: 要显示的浮点数
 * @retval None
 */
void HMI_SendFloat(const char* name, float num);

/**
 * @brief  发送当前状态到串口屏
 * @param  None
 * @retval None
 */
void UartScreen_SendCurrentStatus(void);

// ==================== 矩阵键盘控制函数 ====================

/**
 * @brief  初始化矩阵键盘
 * @param  None
 * @retval uint8_t 初始化状态 (0-成功, 1-失败)
 */
uint8_t MatrixKeypad_Init(void);

/**
 * @brief  扫描矩阵键盘
 * @param  None
 * @retval uint8_t 按键值 (0xFF-无按键)
 */
uint8_t MatrixKeypad_Scan(void);

/**
 * @brief  处理键盘输入
 * @param  key: 按键值
 * @retval uint8_t 处理状态 (0-继续输入, 1-命令完成)
 */
uint8_t MatrixKeypad_ProcessKey(uint8_t key);

/**
 * @brief  显示当前菜单 (通过LED或其他方式)
 * @param  menu_state: 菜单状态
 * @retval None
 */
void MatrixKeypad_DisplayMenu(UI_State_t menu_state);

// ==================== 参数存储函数 ====================

/**
 * @brief  保存参数到Flash
 * @param  preset_index: 预设索引 (0-7)
 * @param  params: 参数结构体
 * @retval uint8_t 保存状态 (0-成功, 1-失败)
 */
uint8_t ParamStorage_Save(uint8_t preset_index, const AD9854_ControlParams_t *params);

/**
 * @brief  从Flash加载参数
 * @param  preset_index: 预设索引 (0-7)
 * @param  params: 参数结构体指针
 * @retval uint8_t 加载状态 (0-成功, 1-失败)
 */
uint8_t ParamStorage_Load(uint8_t preset_index, AD9854_ControlParams_t *params);

/**
 * @brief  获取预设配置列表
 * @param  presets: 预设配置数组
 * @retval uint8_t 有效预设数量
 */
uint8_t ParamStorage_GetPresets(ParamPreset_t presets[MAX_PRESETS]);

// ==================== 通用控制函数 ====================

/**
 * @brief  外部控制主循环处理
 * @param  None
 * @retval None
 */
void ExternalControl_Process(void);

/**
 * @brief  解析频率字符串
 * @param  freq_str: 频率字符串 (如 "5.5MHz", "1000Hz")
 * @retval double 频率值 (Hz)，-1表示解析失败
 */
double ExternalControl_ParseFrequency(const char *freq_str);

/**
 * @brief  解析幅度字符串
 * @param  amp_str: 幅度字符串 (如 "500mV", "1.5V")
 * @retval double 幅度值 (mV)，-1表示解析失败
 */
double ExternalControl_ParseAmplitude(const char *amp_str);

/**
 * @brief  格式化频率显示
 * @param  frequency_hz: 频率 (Hz)
 * @param  buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval None
 */
void ExternalControl_FormatFrequency(double frequency_hz, char *buffer, uint8_t buffer_size);

/**
 * @brief  格式化幅度显示
 * @param  amplitude_mv: 幅度 (mV)
 * @param  buffer: 输出缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval None
 */
void ExternalControl_FormatAmplitude(double amplitude_mv, char *buffer, uint8_t buffer_size);

#ifdef __cplusplus
}
#endif

#endif /* __EXTERNAL_CONTROL_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
